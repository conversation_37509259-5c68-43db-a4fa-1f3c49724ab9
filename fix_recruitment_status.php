<?php

// <PERSON><PERSON><PERSON> to fix recruitment application status from 'pending' to 'new'
// Run this script in the root directory of your Laravel application

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Botble\Recruitment\Enums\RecruitmentApplicationStatusEnum;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "Starting to fix recruitment application status...\n";
    
    // Update 'pending' to 'new'
    $pendingCount = DB::table('recruitment_applications')
        ->where('status', 'pending')
        ->count();
    
    if ($pendingCount > 0) {
        DB::table('recruitment_applications')
            ->where('status', 'pending')
            ->update(['status' => RecruitmentApplicationStatusEnum::NEW]);
        echo "Updated {$pendingCount} records from 'pending' to 'new'\n";
    } else {
        echo "No records with 'pending' status found\n";
    }
    
    // Update other old status values
    $reviewingCount = DB::table('recruitment_applications')
        ->where('status', 'reviewing')
        ->count();
    
    if ($reviewingCount > 0) {
        DB::table('recruitment_applications')
            ->where('status', 'reviewing')
            ->update(['status' => RecruitmentApplicationStatusEnum::PROCESSING]);
        echo "Updated {$reviewingCount} records from 'reviewing' to 'processing'\n";
    }
    
    $approvedCount = DB::table('recruitment_applications')
        ->where('status', 'approved')
        ->count();
    
    if ($approvedCount > 0) {
        DB::table('recruitment_applications')
            ->where('status', 'approved')
            ->update(['status' => RecruitmentApplicationStatusEnum::PASSED]);
        echo "Updated {$approvedCount} records from 'approved' to 'passed'\n";
    }
    
    $rejectedCount = DB::table('recruitment_applications')
        ->where('status', 'rejected')
        ->count();
    
    if ($rejectedCount > 0) {
        DB::table('recruitment_applications')
            ->where('status', 'rejected')
            ->update(['status' => RecruitmentApplicationStatusEnum::FAILED]);
        echo "Updated {$rejectedCount} records from 'rejected' to 'failed'\n";
    }
    
    echo "Status fix completed successfully!\n";
    
    // Show current status distribution
    echo "\nCurrent status distribution:\n";
    $statusCounts = DB::table('recruitment_applications')
        ->select('status', DB::raw('count(*) as count'))
        ->groupBy('status')
        ->get();
    
    foreach ($statusCounts as $status) {
        echo "- {$status->status}: {$status->count}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please run this script manually or check your database connection.\n";
}

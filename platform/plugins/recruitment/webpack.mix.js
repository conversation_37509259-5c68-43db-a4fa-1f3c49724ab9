const mix = require('laravel-mix')
const path = require('path')

const directory = path.basename(path.resolve(__dirname))
const source = `platform/plugins/${directory}`
const dist = `public/vendor/core/plugins/${directory}`

mix
    .css(`${source}/resources/css/application.css`, `${dist}/css`)
    .js(`${source}/resources/js/recruitment-admin.js`, `${dist}/js`)
    .js(`${source}/resources/js/recruitment-applications.js`, `${dist}/js`)

if (mix.inProduction()) {
    mix
        .copy(`${dist}/css/application.css`, `${source}/public/css`)
        .copy(`${dist}/js/recruitment-admin.js`, `${source}/public/js`)
        .copy(`${dist}/js/recruitment-applications.js`, `${source}/public/js`)
}

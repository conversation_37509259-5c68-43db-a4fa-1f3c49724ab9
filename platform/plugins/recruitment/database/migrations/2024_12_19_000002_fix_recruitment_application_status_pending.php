<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Botble\Recruitment\Enums\RecruitmentApplicationStatusEnum;

return new class extends Migration
{
    public function up(): void
    {
        // Update any remaining 'pending' status to 'new'
        DB::table('recruitment_applications')
            ->where('status', 'pending')
            ->update(['status' => RecruitmentApplicationStatusEnum::NEW]);
            
        // Also update any other old status values that might exist
        DB::table('recruitment_applications')
            ->where('status', 'reviewing')
            ->update(['status' => RecruitmentApplicationStatusEnum::PROCESSING]);
            
        DB::table('recruitment_applications')
            ->where('status', 'approved')
            ->update(['status' => RecruitmentApplicationStatusEnum::PASSED]);
            
        DB::table('recruitment_applications')
            ->where('status', 'rejected')
            ->update(['status' => RecruitmentApplicationStatusEnum::FAILED]);
    }

    public function down(): void
    {
        // Revert back to old status values
        DB::table('recruitment_applications')
            ->where('status', RecruitmentApplicationStatusEnum::NEW)
            ->update(['status' => 'pending']);
            
        DB::table('recruitment_applications')
            ->where('status', RecruitmentApplicationStatusEnum::PROCESSING)
            ->update(['status' => 'reviewing']);
            
        DB::table('recruitment_applications')
            ->where('status', RecruitmentApplicationStatusEnum::PASSED)
            ->update(['status' => 'approved']);
            
        DB::table('recruitment_applications')
            ->where('status', RecruitmentApplicationStatusEnum::FAILED)
            ->update(['status' => 'rejected']);
    }
};

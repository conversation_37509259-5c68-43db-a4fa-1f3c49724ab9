<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Botble\Recruitment\Enums\RecruitmentApplicationStatusEnum;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('recruitment_applications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('recruitment_id')->nullable()->constrained('recruitments')->onDelete('set null');
            $table->string('recruitment_name')->nullable();
            $table->string('lang_code', 10)->nullable();
            $table->string('full_name');
            $table->date('birthday');
            $table->enum('gender', ['male', 'female', 'other']);
            $table->string('phone', 20);
            $table->string('email');

            // Location preferences
            $table->foreignId('location_province_1')->nullable()->constrained('recruitment_provinces')->onDelete('set null');
            $table->foreignId('location_district_1')->nullable()->constrained('recruitment_districts')->onDelete('set null');
            $table->foreignId('location_province_2')->nullable()->constrained('recruitment_provinces')->onDelete('set null');
            $table->foreignId('location_district_2')->nullable()->constrained('recruitment_districts')->onDelete('set null');
            $table->foreignId('location_province_3')->nullable()->constrained('recruitment_provinces')->onDelete('set null');
            $table->foreignId('location_district_3')->nullable()->constrained('recruitment_districts')->onDelete('set null');

            // Additional info
            $table->string('old_employee_code', 50)->nullable();
            $table->string('referral_code', 50)->nullable();
            $table->enum('newsletter', ['yes', 'no'])->default('no');
            $table->enum('work_days', ['under_4', '4_to_6', '7_days']);

            // CV file
            $table->string('cv_file_path')->nullable();
            $table->string('cv_file_name')->nullable();
            $table->integer('cv_file_size')->nullable();

            // Status
            $table->string('status', 60)->default(RecruitmentApplicationStatusEnum::NEW); // new, processing, passed, failed
            $table->text('notes')->nullable();

            $table->timestamps();

            $table->index(['recruitment_id', 'status']);
            $table->index(['email']);
            $table->index(['phone']);
            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('recruitment_applications');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Botble\Recruitment\Enums\RecruitmentApplicationStatusEnum;

return new class extends Migration
{
    public function up(): void
    {
        // Update existing status values to new ones
        DB::table('recruitment_applications')
            ->where('status', 'pending')
            ->update(['status' => RecruitmentApplicationStatusEnum::NEW]);

        DB::table('recruitment_applications')
            ->where('status', 'reviewing')
            ->update(['status' => RecruitmentApplicationStatusEnum::PROCESSING]);

        DB::table('recruitment_applications')
            ->where('status', 'approved')
            ->update(['status' => RecruitmentApplicationStatusEnum::PASSED]);

        DB::table('recruitment_applications')
            ->where('status', 'rejected')
            ->update(['status' => RecruitmentApplicationStatusEnum::FAILED]);
    }

    public function down(): void
    {
        // Revert status values back to old ones
        DB::table('recruitment_applications')
            ->where('status', 'new')
            ->update(['status' => 'pending']);

        DB::table('recruitment_applications')
            ->where('status', 'processing')
            ->update(['status' => 'reviewing']);

        DB::table('recruitment_applications')
            ->where('status', 'passed')
            ->update(['status' => 'approved']);

        DB::table('recruitment_applications')
            ->where('status', 'failed')
            ->update(['status' => 'rejected']);
    }
};

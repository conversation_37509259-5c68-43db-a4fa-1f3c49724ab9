$(() => {
    console.log('Recruitment applications JS loaded');

    // Handle export button with current filters
    $(document).on('click', '.export-application-button', function(e) {
        e.preventDefault();
        console.log('Export button clicked');

        // Get base export URL from data attribute
        const baseExportUrl = $(this).data('export-url') || '/admincp/recruitment-applications/export';
        
        // Get current URL with all filters
        const currentUrl = new URL(window.location.href);
        const exportUrl = new URL(baseExportUrl, window.location.origin);

        // Copy all filter parameters to export URL
        currentUrl.searchParams.forEach((value, key) => {
            exportUrl.searchParams.set(key, value);
            console.log(`Adding param: ${key} = ${value}`);
        });

        console.log('Current URL:', currentUrl.toString());
        console.log('Export URL with filters:', exportUrl.toString());

        // Navigate to export URL
        window.location.href = exportUrl.toString();
    });
});
<div class="card">
    <div class="card-header">
        <h4 class="card-title">
            <i class="fa fa-users"></i> Đ<PERSON>n <PERSON>ng tuyển ({{ $applications->total() }})
        </h4>
    </div>
    <div class="card-body">
        @if($applications->count() > 0)
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Họ và tên</th>
                            <th>Email</th>
                            <th>Số điện thoại</th>
                            <th>Trạng thái</th>
                            <th><PERSON><PERSON><PERSON> ứng tuyển</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($applications as $application)
                            <tr>
                                <td>
                                    <strong>{{ $application->full_name }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        {{ $application->gender_label }} -
                                        {{ $application->birthday ? $application->birthday->format('d/m/Y') : 'N/A' }}
                                    </small>
                                </td>
                                <td>
                                    <a href="mailto:{{ $application->email }}">{{ $application->email }}</a>
                                </td>
                                <td>
                                    <a href="tel:{{ $application->phone }}">{{ $application->phone }}</a>
                                </td>
                                <td>
                                    {!! $application->status->toHtml() !!}
                                </td>
                                <td>
                                    {{ $application->created_at->format('d/m/Y H:i') }}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('recruitment-applications.show', $application->id) }}"
                                           class="btn btn-primary btn-sm"
                                           data-bs-toggle="tooltip"
                                           title="Xem chi tiết">
                                            <i class="fa fa-eye"></i>
                                        </a>

                                        @if($application->cv_file_path)
                                            <a href="{{ route('recruitment-applications.download-cv', $application->id) }}"
                                               class="btn btn-success btn-sm"
                                               target="_blank"
                                               data-bs-toggle="tooltip"
                                               title="Tải CV">
                                                <i class="fa fa-download"></i>
                                            </a>
                                        @endif

                                        <a href="{{ route('recruitment-applications.edit', $application->id) }}"
                                           class="btn btn-warning btn-sm"
                                           data-bs-toggle="tooltip"
                                           title="Chỉnh sửa">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            @if($applications->hasPages())
                <div class="d-flex justify-content-center">
                    {{ $applications->links() }}
                </div>
            @endif

            <div class="mt-3">
                <a href="{{ route('recruitment-applications.index') }}?recruitment_id={{ $recruitment->id }}"
                   class="btn btn-primary">
                    <i class="fa fa-list"></i> Xem tất cả đơn ứng tuyển
                </a>
            </div>
        @else
            <div class="text-center py-4">
                <i class="fa fa-inbox fa-3x text-muted mb-3"></i>
                <p class="text-muted">Chưa có đơn ứng tuyển nào cho vị trí này.</p>
            </div>
        @endif
    </div>
</div>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    color: #6c757d;
}

.table td {
    vertical-align: middle;
    font-size: 13px;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.badge {
    font-size: 0.7em;
    padding: 0.35em 0.65em;
}
</style>

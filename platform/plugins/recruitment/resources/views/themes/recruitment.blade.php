<section class="section-page-recruitment-detail section-py xl:rem:pt-[118px]">
    <div class="container">
        <div class="row">
            <div class="col lg:w-9/12">
                <div class="job-title">
                    <h1 class="title-40 font-bold text-Primary-S1 mb-3">{{ $recruitment->name }}</h1>
                </div>
                <div class="job-infor flex gap-3 md:justify-between md:items-center pb-3 border-b border-Primary-S1 mb-base flex-wrap md:flex-row flex-col">
                    <div class="flex items-center gap-3">
                        <ul class="list-date">
                            <li class="flex items-center text-Neutral-800"><i class="fa-light fa-calendar text-xl"></i><span>{{ __('Ngày đăng:') }} {{ $recruitment->created_at->format('d/m/Y') }}</span></li>
                            <li class="flex items-center text-Neutral-800"><i class="fa-light fa-calendar text-xl"></i><span>{{ __('Hạn nộp:') }} {{ $recruitment->deadline ? $recruitment->deadline->format('d/m/Y') : '-' }}</span></li>
                        </ul>
                    </div>
                    <div class="flex items-center gap-5">
                        <div class="bookmark">
                            <p>{{ __('Lưu') }}</p><i class="fa-light fa-bookmark"></i>
                        </div>
                        <ul class="social flex items-center gap-5">
                            <li> <a class="flex-center sq-10 rounded-full border-Neutral-500-main text-Neutral-500-main border hover:bg-Primary-3 hover:text-white hover:border-Primary-3" href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->url()) }}" target="_blank"> <i class="fa-brands fa-facebook-f"></i></a></li>
                            <li> <a class="flex-center sq-10 rounded-full border-Neutral-500-main text-Neutral-500-main border hover:bg-Primary-3 hover:text-white hover:border-Primary-3" href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->url()) }}" target="_blank"> <i class="fa-brands fa-linkedin-in"></i></a></li>
                        </ul>
                    </div>
                </div>
                <div class="flex flex-col gap-base">
                    <div class="job-jd bg-Neutral-50 p-5">
                        <ul>
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-user text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Số lượng:') }}</div>
                                    <div class="value">{{ $recruitment->quantity ?? '-' }}</div>
                                </div>
                            </li>
                            @if($recruitment->salary)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-dollar-sign text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Mức lương:') }}</div>
                                    <div class="value">{{ $recruitment->salary }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->province || $recruitment->district)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-location-dot text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Địa điểm làm việc:') }}</div>
                                    <div class="value">
                                        @if($recruitment->district && $recruitment->province)
                                        {{ $recruitment->district->name }}, {{ $recruitment->province->name }}
                                        @elseif($recruitment->province)
                                        {{ $recruitment->province->name }}
                                        @endif
                                    </div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->work_mode)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-briefcase text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Chế độ làm việc:') }}</div>
                                    <div class="value">{{ $recruitment->work_mode }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->product_benefits)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-gem text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Ưu đãi sản phẩm:') }}</div>
                                    <div class="value">{{ $recruitment->product_benefits }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->business_revenue)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-dollar-sign text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Thưởng doanh số:') }}</div>
                                    <div class="value">{{ $recruitment->business_revenue }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->working_hours)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-timer text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Thời gian làm việc:') }}</div>
                                    <div class="value">{{ $recruitment->working_hours }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->promotion_opportunities)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-handshake text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Cơ hội thăng tiến:') }}</div>
                                    <div class="value">{{ $recruitment->promotion_opportunities }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->internal_bonding_programs)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-people-group text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Chương trình gắn kết nội bộ:') }}</div>
                                    <div class="value">{{ $recruitment->internal_bonding_programs }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->job_level)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-bookmark text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Cấp bậc:') }}</div>
                                    <div class="value">{{ $recruitment->job_level }}</div>
                                </div>
                            </li>
                            @endif
                            @if($recruitment->health_checkups)
                            <li class="flex items-center gap-3 mb-3">
                                <div class="title flex items-center gap-3"><i class="fa-light fa-kit-medical text-xl"></i></div>
                                <div class="wrap">
                                    <div class="name">{{ __('Khám sức khỏe:') }}</div>
                                    <div class="value">{{ $recruitment->health_checkups }}</div>
                                </div>
                            </li>
                            @endif
                        </ul>
                    </div>
                    @if($recruitment->content)
                    <div class="job-content">
                        <div class="title">{{ __('Mô tả công việc') }}</div>
                        <div class="content format-content">
                            {!! $recruitment->content !!}
                        </div>
                    </div>
                    @endif

                    @if($recruitment->requirements)
                    <div class="job-content">
                        <div class="title">{{ __('Yêu cầu công việc') }}</div>
                        <div class="content format-content">
                            {!! $recruitment->requirements !!}
                        </div>
                    </div>
                    @endif

                    @if($recruitment->benefits)
                    <div class="job-content">
                        <div class="title">{{ __('Quyền lợi') }}</div>
                        <div class="content format-content">
                            {!! $recruitment->benefits !!}
                        </div>
                    </div>
                    @endif
                    <div class="flex">
                        @php
                        $recruitment_form_content = theme_option('recruitment_form_content');
                        $recruitment_form_note = theme_option('recruitment_form_note');

                        // Lấy URL của page ứng tuyển
                        $applicationPageId = theme_option('recruitment_application_page_id', setting('recruitment_application_page_id'));
                        $applicationPageUrl = null;
                        if ($applicationPageId) {
                        $applicationPage = \Botble\Page\Models\Page::find($applicationPageId);
                        if ($applicationPage) {
                        $applicationPageUrl = $applicationPage->url . '?recruitment_id=' . $recruitment->id;
                        }
                        }
                        @endphp

                        @if($applicationPageUrl)
                        <a class="btn btn-btn-primary secondary" href="{{ $applicationPageUrl }}">
                            <span>{{ __('Ứng tuyển ngay') }}</span>
                            <div class="btn-icon btn-lined">
                                <span class="icon to-right">
                                    <i class="fa-regular fa-arrow-right"></i>
                                    <i class="fa-regular fa-arrow-right"></i>
                                </span>
                            </div>
                        </a>
                        @else
                        <a class="btn btn-btn-primary secondary" href="#">
                            <span>{{ __('Ứng tuyển ngay') }}</span>
                            <div class="btn-icon btn-lined">
                                <span class="icon to-right">
                                    <i class="fa-regular fa-arrow-right"></i>
                                    <i class="fa-regular fa-arrow-right"></i>
                                </span>
                            </div>
                        </a>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col lg:w-3/12">
                <div class="flex flex-col gap-base">
                    <div class="box-policy bg-Primary-S2 p-6 rem:rounded-[20px] flex flex-col gap-5">
                        <div class="text-2xl font-bold text-Primary-S1">{{ __('Lưu ý') }}</div>
                        <div class="format-content">
                            {!! $recruitment_form_content !!}
                            <p style="color: red"><strong>{!! $recruitment_form_note !!}</strong></p>
                        </div>
                    </div>
                    <div class="box-form">
                        <div class="mb-base text-2xl font-bold text-Primary-S1">{{ __('Tìm việc làm') }}</div>
                        <form action="{{ $recruitment_page_url }}" method="get">

                            <div class="wrap-form form-style-general flex flex-col gap-5 shadow-Dropshadow-Light py-6 px-5 rem:rounded-b-[40px]">
                                <div class="form-group">
                                    <input type="text" placeholder="{{ __('Tìm kiếm') }}" name="keyword" />
                                </div>
                                <div class="form-group">
                                    <label for="category_id">{{ __('Vị trí công việc') }}</label>
                                    <select name="category_id" id="category_id" placeholder="{{ __('Vị trí công việc') }}">
                                        <option value="">{{ __('Chọn vị trí công việc') }}</option>
                                        @foreach($categories as $category)
                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="province_id">{{ __('Tỉnh/Thành phố') }}</label>
                                    <select name="province_id" id="province_id" class="province-select" data-target="district_id" placeholder="{{ __('Tỉnh/Thành phố') }}">
                                        <option value="">{{ __('Chọn tỉnh/thành phố') }}</option>
                                        @foreach($provinces as $province)
                                        <option value="{{ $province->id }}">{{ $province->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="district_id">{{ __('Quận/Huyện') }}</label>
                                    <select name="district_id" id="district_id" class="district-select" placeholder="{{ __('Quận/Huyện') }}">
                                        <option value="">{{ __('Chọn quận/huyện') }}</option>
                                        <!-- Districts will be loaded via AJAX based on province selection -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="work_type_id">{{ __('Hình thức làm việc') }}</label>
                                    <select name="work_type_id" id="work_type_id" placeholder="{{ __('Hình thức làm việc') }}">
                                        <option value="">{{ __('Chọn hình thức làm việc') }}</option>
                                        @foreach($workTypes as $workType)
                                        <option value="{{ $workType->id }}">{{ $workType->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <button class="btn btn-btn-primary secondary w-full flex-center"> <span>{{ __('Tìm kiếm') }}</span>
                                    <div class="btn-icon btn-lined"><span class="icon to-right"><i class="fa-regular fa-arrow-right"></i><i class="fa-regular fa-arrow-right"></i></span></div>
                                </button>
                            </div>
                        </form>

                    </div>
                    <div class="box-other-job rounded-xl bg-Neutral-50 p-5">
                        <div class="title text-2xl font-bold text-Primary-S1 pb-3 border-b border-Primary-S1 mb-3 md:mb-6">{{ __('Vị trí khác') }}</div>
                        <ul class="list-other-job">
                            @forelse($relatedRecruitments as $relatedRecruitment)
                            <li>
                                <a href="{{ $relatedRecruitment->url }}" class="block">
                                    <div class="title mb-3 font-bold text-lg text-Primary-S1">{{ $relatedRecruitment->name }}</div>
                                    <ul class="list-infor text-Neutral-800">
                                        <li class="quantity"><i class="fa-light fa-user"></i><span class="text-sm">{{ __('Số lượng:') }} {{ $relatedRecruitment->quantity ?? '-' }}</span></li>
                                        @if($relatedRecruitment->district && $relatedRecruitment->province)
                                        <li class="location"><i class="fa-light fa-location-dot"></i><span class="text-sm">{{ $relatedRecruitment->district->name }}, {{ $relatedRecruitment->province->name }}</span></li>
                                        @elseif($relatedRecruitment->province)
                                        <li class="location"><i class="fa-light fa-location-dot"></i><span class="text-sm">{{ $relatedRecruitment->province->name }}</span></li>
                                        @endif
                                        <li class="expiration"><i class="fa-light fa-calendar"></i><span class="text-sm">{{ __('Hạn nộp:') }} {{ $relatedRecruitment->deadline ? $relatedRecruitment->deadline->format('d/m/Y') : '-' }}</span></li>
                                    </ul>
                                </a>
                            </li>
                            @empty
                            <li>
                                <div class="text-center text-Neutral-800">{{ __('Không có vị trí tuyển dụng liên quan') }}</div>
                            </li>
                            @endforelse
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

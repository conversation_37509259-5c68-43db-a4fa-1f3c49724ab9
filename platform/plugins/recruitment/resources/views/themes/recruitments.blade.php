{!! Theme::partial('breadcrumbs') !!}
@if($page->image)
<section class="page-banner-main">
    <div class="img img-ratio pt-[calc(500/1920*100%)]">
        <img class="lozad undefined" data-src="{!! RvMedia::getImageUrl($page->image) !!}" alt="" src="{!! RvMedia::getImageUrl($page->image) !!}" data-loaded="true">
    </div>
</section>
@endif


<section class="section-page-recruitment-list section-py xl:rem:pt-[118px]">
	<div class="container">
		<div class="text-center">
			<h1 class="title-40 mb-8 font-bold">{{ $page->name ?? 'Cơ hội nghề nghiệp ở CHAGEE' }}</h1>
		</div>
		<form class="wrap-form-recruitment form-style-general shadow-Dropshadow-Light bg-white rem:rounded-b-[40px] lg:p-10 lg:px-16 p-5" method="GET" action="{{ request()->url() }}">
			<div class="form-group md:col-span-full">
				<input type="text" placeholder="{{ __('Tìm kiếm') }}" name="keyword" value="{{ request('keyword') }}">
			</div>
			<div class="form-group">
				<select name="category_id" placeholder="{{ __('Vị trí công việc') }}">
					<option value="">{{ __('Chọn vị trí công việc') }}</option>
					@foreach($categories as $category)
					<option value="{{ $category->id }}" {{ request('category_id') == $category->id ? 'selected' : '' }}>
						{{ $category->name }}
					</option>
					@endforeach
				</select>
			</div>
			<div class="form-group">
				<select name="province_id" id="province_id" class="province-select" data-target="district_id" placeholder="{{ __('Địa điểm') }}">
					<option value="">{{ __('Chọn tỉnh/thành phố') }}</option>
					@foreach($provinces as $province)
					<option value="{{ $province->id }}" {{ request('province_id') == $province->id ? 'selected' : '' }}>
						{{ $province->name }}
					</option>
					@endforeach
				</select>
			</div>
			<div class="form-group">
				<select name="district_id" id="district_id" class="district-select" placeholder="{{ __('Quận') }}">
					<option value="">{{ __('Chọn quận/huyện') }}</option>
					@if(request('province_id') && request('district_id'))
					@php
					$selectedDistricts = \Botble\Recruitment\Models\District::where('province_id', request('province_id'))->wherePublished()->get();
					@endphp
					@foreach($selectedDistricts as $district)
					<option value="{{ $district->id }}" {{ request('district_id') == $district->id ? 'selected' : '' }}>
						{{ $district->name }}
					</option>
					@endforeach
					@endif
				</select>
			</div>
			<div class="form-group">
				<select name="work_type_id" placeholder="{{ __('Hình thức làm việc') }}">
					<option value="">{{ __('Chọn hình thức làm việc') }}</option>
					@foreach($workTypes as $workType)
					<option value="{{ $workType->id }}" {{ request('work_type_id') == $workType->id ? 'selected' : '' }}>
						{{ $workType->name }}
					</option>
					@endforeach
				</select>
			</div>
			<div class="frm-btn-wrap flex-center md:col-span-full">
				<button class="btn-btn-primary btn secondary" type="submit">
					<span>{{ __('Tìm kiếm') }}</span>
					<div class="icon-search"><i class="fa-light fa-magnifying-glass text-xl"></i></div>
				</button>
			</div>
		</form>
	</div>
</section>

<section class="section-page-recruitment-list section-py !pt-0">
	<div class="container">
		<div class="wrap-recruitment-list">
			<div class="head-title border-b border-Primary-S1">
				<div class="flex justify-between items-center py-5 text-lg font-bold text-Primary-S1 mx-auto">
					<p>{{ __('Vị trí công việc') }}</p>
					<p>{{ __('Địa điểm') }}</p>
					<p>{{ __('Hình thức') }}</p>
				</div>
			</div>
			<div class="recruitment-list">
				@if($recruitments->count() > 0)
				@foreach ($recruitments as $recruitment)
				<div class="item-recruitment flex gap-8">
					<a class="img rem:sq-[122px] img-full rounded-lg overflow-hidden" href="{{ $recruitment->url }}">
						<img class="lozad undefined" data-src="{{ RvMedia::getImageUrl($recruitment->image, 'medium', false, RvMedia::getDefaultImage()) }}" alt="{{ $recruitment->name }}" />
					</a>
					<div class="content flex-1">
						<div class="title-bookmark flex justify-between lg:items-center">
							<div class="title flex lg:items-center lg:gap-base lg:flex-row flex-wrap flex-col gap-[8px]">
								<h3 class="text-2xl font-bold text-Primary-S1">
									<a href="{{ $recruitment->url }}">{{ $recruitment->name }}</a>
								</h3>
							</div>
							<div class="tags">
									{{-- System tags based on recruitment properties --}}
									{{-- @if($recruitment->is_featured)
                                                <div class="tag" style="background-color: #FB8600; color: white;">Ưu tiên</div>
                                            @endif
                                            @if($recruitment->created_at->diffInDays() <= 7)
                                                <div class="tag new" style="background-color: #28a745; color: white;">Mới</div>
                                            @endif
                                            @if($recruitment->salary && (str_contains(strtolower($recruitment->salary), 'triệu') || str_contains(strtolower($recruitment->salary), 'million')))
                                                <div class="tag hot" style="background-color: #dc3545; color: white;">Hấp dẫn</div>
                                            @endif
                                            @if($recruitment->deadline && $recruitment->deadline->diffInDays() <= 7)
                                                <div class="tag urgent" style="background-color: #ffc107; color: black;">Gấp</div>
                                            @endif --}}

									{{-- Custom tags from database --}}
									@if($recruitment->tags->isNotEmpty())
									@foreach($recruitment->tags as $tag)
									<div class="tag" style="background-color: {{ $tag->color }}; color: {{ $tag->name === 'Ưu tiên' || $tag->name === 'Prior' ? 'black' : ($tag->color === '#ffc107' ? 'black' : 'white') }};">
										{{ $tag->name }}
									</div>
									@endforeach
									@endif
								</div>
							<!-- <div class="bookmark">
                                        <p>{{ __('Lưu') }}</p><i class="fa-light fa-bookmark"></i>
                                    </div> -->
						</div>
						<ul class="wrap-infor">
							<li class="quantity">
								<i class="fa-light fa-user"></i>
								<span>{{ __('Số lượng:') }} {{ $recruitment->quantity ?? __('Không giới hạn') }}</span>
							</li>
							@if($recruitment->salary)
							<li class="salary">
								<i class="fa-light fa-dollar-sign"></i>
								<span>{{ __('Mức lương:') }} {{ $recruitment->salary }}</span>
							</li>
							@endif
							@if($recruitment->province || $recruitment->district)
							<li class="location">
								<i class="fa-light fa-location-dot"></i>
								<span>
									@if($recruitment->district && $recruitment->province)
									{{ $recruitment->district->name }}, {{ $recruitment->province->name }}
									@elseif($recruitment->province)
									{{ $recruitment->province->name }}
									@endif
								</span>
							</li>
							@endif
							@if($recruitment->workTypes->isNotEmpty())
							<li class="work-type">
								<i class="fa-light fa-clock"></i>
								<span>{{ $recruitment->workTypes->pluck('name')->join(', ') }}</span>
							</li>
							@endif
							@if($recruitment->deadline)
							<li class="expiration">
								<i class="fa-light fa-calendar"></i>
								<span>{{ __('Hạn nộp:') }} {{ $recruitment->deadline->format('d/m/Y') }}</span>
							</li>
							@endif
						</ul>
					</div>
				</div>
				@endforeach
				@else
				<div class="no-results text-center py-12">
					<div class="icon mb-4">
						<i class="fa-light fa-briefcase text-6xl text-Neutral-400"></i>
					</div>
					<h4 class="text-2xl font-bold text-Neutral-800 mb-3">{{ __('Không tìm thấy vị trí tuyển dụng nào') }}</h4>
					<p class="text-Neutral-600 mb-6">{{ __('Vui lòng thử lại với từ khóa khác hoặc điều chỉnh bộ lọc tìm kiếm.') }}</p>
					<a href="{{ request()->url() }}" class="btn btn-btn-primary secondary">
						<span>{{ __('Xem tất cả vị trí') }}</span>
						<div class="btn-icon btn-lined">
							<span class="icon to-right">
								<i class="fa-regular fa-arrow-right"></i>
								<i class="fa-regular fa-arrow-right"></i>
							</span>
						</div>
					</a>
				</div>
				@endif
			</div>

			@if($recruitments->hasPages())
			<div class="pagination-wrapper flex justify-center mt-8">
				<div class="pagination-custom">
					{!! $recruitments->appends(request()->query())->links() !!}
				</div>
			</div>
			@endif
		</div>
	</div>
</section>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		// Load districts on page load if province is already selected
		const provinceSelect = document.getElementById('province_id');
		const districtSelect = document.getElementById('district_id');
		const selectedDistrictId = '{{ request("district_id") }}';

		if (provinceSelect && districtSelect && provinceSelect.value && selectedDistrictId) {
			// Wait a bit for Select2 to initialize, then load districts
			setTimeout(function() {
				loadDistrictsForRecruitmentFilter(provinceSelect.value, selectedDistrictId);
			}, 1000);
		}

		function loadDistrictsForRecruitmentFilter(provinceId, selectedDistrictId = '') {
			if (!provinceId) return;

			// Make AJAX request to load districts
			fetch('/ajax/districts?province_id=' + provinceId)
				.then(response => response.json())
				.then(data => {
					// Clear current options except the first one
					districtSelect.innerHTML = '<option value="">Chọn quận/huyện</option>';

					// Add new options
					if (data && data.length > 0) {
						data.forEach(function(district) {
							const option = document.createElement('option');
							option.value = district.id;
							option.textContent = district.name;
							if (selectedDistrictId && district.id == selectedDistrictId) {
								option.selected = true;
							}
							districtSelect.appendChild(option);
						});
					}

					// Refresh Select2 if it's initialized
					if ($(districtSelect).hasClass('select2-hidden-accessible')) {
						$(districtSelect).trigger('change');
					}
				})
				.catch(error => {
					console.error('Error loading districts:', error);
				});
		}
	});
</script>
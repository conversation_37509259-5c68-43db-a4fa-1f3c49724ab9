<?php

return [
    'name' => 'Recruitment',
    'list' => 'Recruitments',
    'create' => 'New recruitment',
    'edit' => 'Edit recruitment',
    'edit_this_recruitment' => 'Edit this recruitment',
    'categories' => 'Categories',
    'create_category' => 'New category',
    'edit_category' => 'Edit category',
    'edit_this_category' => 'Edit this category',
    'tags' => 'Tags',
    'create_tag' => 'New tag',
    'edit_tag' => 'Edit tag',
    'edit_this_tag' => 'Edit this tag',
    'tag_color' => 'Tag Color',
    'work_types' => 'Work Types',
    'create_work_type' => 'New work type',
    'edit_work_type' => 'Edit work type',
    'edit_this_work_type' => 'Edit this work type',
    'category' => 'Category',
    'work_type' => 'Work Type',
    'select_categories' => 'Select categories',
    'select_work_types' => 'Select work types',
    'provinces' => 'Provinces',
    'province' => 'Province',
    'create_province' => 'New province',
    'edit_province' => 'Edit province',
    'edit_this_province' => 'Edit this province',
    'districts' => 'Districts',
    'district' => 'District',
    'create_district' => 'New district',
    'edit_district' => 'Edit district',
    'edit_this_district' => 'Edit this district',
    'quantity' => 'Quantity',
    'quantity_placeholder' => 'Number of positions',
    'salary' => 'Salary',
    'salary_placeholder' => 'Enter salary range',
    'work_mode' => 'Work Mode',
    'work_mode_placeholder' => 'e.g., Hybrid/Onsite/Remote',
    'product_benefits' => 'Product Benefits',
    'product_benefits_placeholder' => 'Enter product benefits and perks',
    'business_revenue' => 'Business Revenue',
    'business_revenue_placeholder' => 'Enter business revenue information',
    'working_hours' => 'Working Hours',
    'working_hours_placeholder' => 'e.g., 09h00 - 18h00, Mon - Fri',
    'promotion_opportunities' => 'Promotion Opportunities',
    'promotion_opportunities_placeholder' => 'Enter promotion and career advancement opportunities',
    'internal_bonding_programs' => 'Internal Bonding Programs',
    'internal_bonding_programs_placeholder' => 'Enter team building and internal programs',
    'job_level' => 'Job Level',
    'job_level_placeholder' => 'e.g., Junior, Senior, Manager',
    'health_checkups' => 'Health Checkups',
    'health_checkups_placeholder' => 'Enter health checkup benefits and schedule',
    'deadline' => 'Deadline',
    'deadline_placeholder' => 'Application deadline',
    'job_description' => 'Job Description',
    'job_description_placeholder' => 'Enter job description',
    'job_requirements' => 'Job Requirements',
    'job_requirements_placeholder' => 'Enter job requirements',
    'job_benefits' => 'Benefits',
    'job_benefits_placeholder' => 'Enter job benefits',
    'select_province' => 'Select province',
    'select_district' => 'Select district',

    // Applications
    'applications' => 'Applications',
    'create_application' => 'Create Application',
    'view_application' => 'View Application: :name',
    'edit_application' => 'Edit Application: :name',
    'application_information' => 'Application Information',
    'application_details' => 'Application Details',
    'applicant_info' => 'Applicant Information',
    'work_preferences' => 'Work Preferences',
    'location_preferences' => 'Location Preferences',
    'status_notes' => 'Status & Notes',
    'download_cv' => 'Download CV',
    'update_status' => 'Update Status',
    'application_status' => 'Application Status',
    'application_notes' => 'Notes',

    // Application form fields
    'full_name' => 'Full Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'gender' => 'Gender',
    'birthday' => 'Birthday',
    'position' => 'Position',

    // Status labels
    'status_pending' => 'Pending',
    'status_reviewing' => 'Reviewing',
    'status_approved' => 'Approved',
    'status_rejected' => 'Rejected',

    // Gender labels
    'gender_male' => 'Male',
    'gender_female' => 'Female',
    'gender_other' => 'Other',

    // Export
    'export_applications' => 'Export Applications',
    'export_applications_description' => 'Export application data to Excel or CSV file',

    // Application statuses
    'application_statuses' => [
        'new' => 'New',
        'processing' => 'Processing',
        'passed' => 'Passed',
        'failed' => 'Failed',
    ],
];

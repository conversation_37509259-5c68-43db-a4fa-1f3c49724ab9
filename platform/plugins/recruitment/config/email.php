<?php

return [
    'name' => 'plugins/recruitment::recruitment.settings.email.title',
    'description' => 'plugins/recruitment::recruitment.settings.email.description',
    'templates' => [
        'application-notice' => [
            'title' => 'plugins/recruitment::recruitment.settings.email.templates.application_notice_title',
            'description' => 'plugins/recruitment::recruitment.settings.email.templates.application_notice_description',
            'subject' => 'plugins/recruitment::recruitment.settings.email.templates.application_notice_subject',
            'can_off' => true,
            'variables' => [
                'applicant_name' => 'plugins/recruitment::recruitment.settings.email.templates.applicant_name',
                'applicant_email' => 'plugins/recruitment::recruitment.settings.email.templates.applicant_email',
                'applicant_phone' => 'plugins/recruitment::recruitment.settings.email.templates.applicant_phone',
                'applicant_birthday' => 'plugins/recruitment::recruitment.settings.email.templates.applicant_birthday',
                'applicant_gender' => 'plugins/recruitment::recruitment.settings.email.templates.applicant_gender',
                'recruitment_name' => 'plugins/recruitment::recruitment.settings.email.templates.recruitment_name',
                'recruitment_url' => 'plugins/recruitment::recruitment.settings.email.templates.recruitment_url',
                'location_province_1' => 'plugins/recruitment::recruitment.settings.email.templates.location_province_1',
                'location_district_1' => 'plugins/recruitment::recruitment.settings.email.templates.location_district_1',
                'old_employee_code' => 'plugins/recruitment::recruitment.settings.email.templates.old_employee_code',
                'referral_code' => 'plugins/recruitment::recruitment.settings.email.templates.referral_code',
                'cv_file_name' => 'plugins/recruitment::recruitment.settings.email.templates.cv_file_name',
                'cv_download_url' => 'plugins/recruitment::recruitment.settings.email.templates.cv_download_url',
                'application_date' => 'plugins/recruitment::recruitment.settings.email.templates.application_date',
                'admin_url' => 'plugins/recruitment::recruitment.settings.email.templates.admin_url',
            ],
        ],
        'application-confirmation' => [
            'title' => 'plugins/recruitment::recruitment.settings.email.templates.application_confirmation_title',
            'description' => 'plugins/recruitment::recruitment.settings.email.templates.application_confirmation_description',
            'subject' => 'plugins/recruitment::recruitment.settings.email.templates.application_confirmation_subject',
            'can_off' => true,
            'variables' => [
                'applicant_name' => 'plugins/recruitment::recruitment.settings.email.templates.applicant_name',
                'recruitment_name' => 'plugins/recruitment::recruitment.settings.email.templates.recruitment_name',
                'recruitment_url' => 'plugins/recruitment::recruitment.settings.email.templates.recruitment_url',
                'application_date' => 'plugins/recruitment::recruitment.settings.email.templates.application_date',
                'company_name' => 'plugins/recruitment::recruitment.settings.email.templates.company_name',
                'contact_email' => 'plugins/recruitment::recruitment.settings.email.templates.contact_email',
            ],
        ],
    ],
];

<?php

namespace Bo<PERSON>ble\Recruitment\Exports;

use Bo<PERSON><PERSON>\Recruitment\Models\RecruitmentApplication;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Collection;

class RecruitmentApplicationExport implements FromCollection, WithHeadings, WithMapping, WithEvents
{
    protected $query;

    public function __construct($query = null)
    {
        $this->query = $query;
    }

    public function collection()
    {
        if ($this->query) {
            // Use the filtered query from table
            return $this->query->get();
        }

        // Fallback to all data if no query provided
        return RecruitmentApplication::query()
            ->with([
                'recruitment',
                'locationProvince1',
                'locationDistrict1',
                'locationProvince2',
                'locationDistrict2',
                'locationProvince3',
                'locationDistrict3'
            ])
            ->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Họ và tên',
            'Vị trí ứng tuyển',
            'Email',
            'Số điện thoại',
            'Giới tính',
            'Ngày sinh',
            'Tỉnh/TP ưu tiên 1',
            'Quận/Huyện ưu tiên 1',
            'Tỉnh/TP ưu tiên 2',
            'Quận/Huyện ưu tiên 2',
            'Tỉnh/TP ưu tiên 3',
            'Quận/Huyện ưu tiên 3',
            'Mã nhân viên cũ',
            'Mã giới thiệu',
            'Đăng ký nhận tin',
            'Có thể làm ca xoay',
            'Có thể làm cuối tuần',
            'Có thể làm ngày lễ',
            'Số ngày làm việc',
            'Thời gian gắn bó',
            'Tên file CV',
            'Kích thước CV',
            'Trạng thái',
            'Ghi chú',
            'Ngày tạo',
            'Ngày cập nhật',
        ];
    }

    public function map($application): array
    {
        return [
            $application->id,
            $application->full_name,
            $application->recruitment_name,
            $application->email,
            $application->phone,
            $application->gender_label,
            $application->birthday ? $application->birthday->format('d/m/Y') : '',
            $application->locationProvince1->name ?? '',
            $application->locationDistrict1->name ?? '',
            $application->locationProvince2->name ?? '',
            $application->locationDistrict2->name ?? '',
            $application->locationProvince3->name ?? '',
            $application->locationDistrict3->name ?? '',
            $application->old_employee_code ?? '',
            $application->referral_code ?? '',
            $application->newsletter === 'yes' ? 'Có' : 'Không',
            $application->can_work_shifts_label,
            $application->can_work_weekends_label,
            $application->can_work_holidays_label,
            $application->work_days_label,
            $application->commitment_duration_label,
            $application->cv_file_name ?? '',
            $application->cv_file_size_formatted,
            $application->status->label(),
            $application->notes ?? '',
            $application->created_at ? $application->created_at->format('d/m/Y H:i') : '',
            $application->updated_at ? $application->updated_at->format('d/m/Y H:i') : '',
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $totalRows = $this->collection()->count() + 1;

                // Style header row
                $event->sheet->getDelegate()->getStyle('A1:AA1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'color' => ['argb' => 'FFFFFF'],
                    ],
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => ['argb' => '366092'],
                    ],
                ]);

                // Auto-size columns
                foreach (range('A', 'AA') as $column) {
                    $event->sheet->getDelegate()->getColumnDimension($column)->setAutoSize(true);
                }

                // Align email and phone columns to left
                $event->sheet->getDelegate()->getStyle('D1:D' . $totalRows)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
                $event->sheet->getDelegate()->getStyle('E1:E' . $totalRows)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

                // Format status column with colors
                for ($index = 2; $index <= $totalRows; $index++) {
                    $statusCell = $event->sheet->getDelegate()->getCell('X' . $index);
                    $statusValue = $statusCell->getValue();

                    $statusColor = $event->sheet->getDelegate()->getStyle('X' . $index)->getFont()->getColor();

                    switch ($statusValue) {
                        case 'Mới':
                            $statusColor->setARGB('0d6efd'); // primary
                            break;
                        case 'Đang xử lý':
                            $statusColor->setARGB('ffc107'); // warning
                            break;
                        case 'Đạt':
                            $statusColor->setARGB('198754'); // success
                            break;
                        case 'Không đạt':
                            $statusColor->setARGB('dc3545'); // danger
                            break;
                        // Keep old statuses for backward compatibility
                        case 'Chờ xử lý':
                            $statusColor->setARGB('ffc107');
                            break;
                        case 'Đang xem xét':
                            $statusColor->setARGB('0dcaf0');
                            break;
                        case 'Đã duyệt':
                            $statusColor->setARGB('198754');
                            break;
                        case 'Từ chối':
                            $statusColor->setARGB('dc3545');
                            break;
                    }
                }
            },
        ];
    }
}

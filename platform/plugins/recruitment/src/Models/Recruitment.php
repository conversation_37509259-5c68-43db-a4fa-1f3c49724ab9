<?php

namespace Bo<PERSON>ble\Recruitment\Models;

use Bo<PERSON>ble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Slug\Traits\HasSlug;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Recruitment extends BaseModel
{
    use HasSlug;

    protected $table = 'recruitments';

    protected $fillable = [
        'name',
        'description',
        'content',
        'requirements',
        'benefits',
        'status',
        'province_id',
        'district_id',
        'quantity',
        'salary',
        'work_mode',
        'product_benefits',
        'business_revenue',
        'working_hours',
        'promotion_opportunities',
        'internal_bonding_programs',
        'job_level',
        'health_checkups',
        'deadline',
        'image',
        'is_featured',
        'author_id',
        'author_type',
        'views',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'deadline' => 'datetime',
        'is_featured' => 'boolean',
    ];

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(RecruitmentCategory::class, 'recruitment_category', 'recruitment_id', 'category_id');
    }

    public function workTypes(): BelongsToMany
    {
        return $this->belongsToMany(WorkType::class, 'recruitment_work_type', 'recruitment_id', 'work_type_id');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(
            RecruitmentTag::class,
            'recruitment_tag_pivot',
            'recruitment_id',
            'tag_id'
        )->withTimestamps();
    }

    public function province(): BelongsTo
    {
        return $this->belongsTo(Province::class, 'province_id')->withDefault();
    }

    public function district(): BelongsTo
    {
        return $this->belongsTo(District::class, 'district_id')->withDefault();
    }

    public function author(): MorphTo
    {
        return $this->morphTo()->withDefault();
    }

    public function getUrlAttribute(): string
    {
        return route('public.recruitment', $this->slugable->key ?? $this->id);
    }
}

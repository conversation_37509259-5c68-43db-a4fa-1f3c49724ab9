<?php

namespace Bo<PERSON>ble\Recruitment\Models;

use Bo<PERSON>ble\Base\Casts\SafeContent;
use <PERSON><PERSON>ble\Base\Models\BaseModel;
use <PERSON><PERSON><PERSON>\Recruitment\Enums\RecruitmentApplicationStatusEnum;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RecruitmentApplication extends BaseModel
{
    protected $table = 'recruitment_applications';

    protected $fillable = [
        'recruitment_id',
        'recruitment_name',
        'lang_code',
        'full_name',
        'birthday',
        'gender',
        'phone',
        'email',
        'location_province_1',
        'location_district_1',
        'location_province_2',
        'location_district_2',
        'location_province_3',
        'location_district_3',
        'old_employee_code',
        'referral_code',
        'newsletter',
        'can_work_shifts',
        'can_work_weekends',
        'can_work_holidays',
        'work_days',
        'commitment_duration',
        'cv_file_path',
        'cv_file_name',
        'cv_file_size',
        'status',
        'notes',
    ];

    protected $casts = [
        'birthday' => 'date',
        'full_name' => SafeContent::class,
        'recruitment_name' => SafeContent::class,
        'notes' => SafeContent::class,
        'status' => RecruitmentApplicationStatusEnum::class,
    ];

    public function setBirthdayAttribute($value)
    {
        if ($value && is_string($value)) {
            // Convert DD/MM/YYYY to YYYY-MM-DD for database storage
            if (preg_match('/^(\d{2})\/(\d{2})\/(\d{4})$/', $value, $matches)) {
                $this->attributes['birthday'] = $matches[3] . '-' . $matches[2] . '-' . $matches[1];
            } else {
                $this->attributes['birthday'] = $value;
            }
        } else {
            $this->attributes['birthday'] = $value;
        }
    }

    public function recruitment(): BelongsTo
    {
        return $this->belongsTo(Recruitment::class, 'recruitment_id')->withDefault();
    }

    public function locationProvince1(): BelongsTo
    {
        return $this->belongsTo(Province::class, 'location_province_1')->withDefault();
    }

    public function locationDistrict1(): BelongsTo
    {
        return $this->belongsTo(District::class, 'location_district_1')->withDefault();
    }

    public function locationProvince2(): BelongsTo
    {
        return $this->belongsTo(Province::class, 'location_province_2')->withDefault();
    }

    public function locationDistrict2(): BelongsTo
    {
        return $this->belongsTo(District::class, 'location_district_2')->withDefault();
    }

    public function locationProvince3(): BelongsTo
    {
        return $this->belongsTo(Province::class, 'location_province_3')->withDefault();
    }

    public function locationDistrict3(): BelongsTo
    {
        return $this->belongsTo(District::class, 'location_district_3')->withDefault();
    }



    public function getGenderLabelAttribute(): string
    {
        switch ($this->gender) {
            case 'male':
                return 'Nam';
            case 'female':
                return 'Nữ';
            case 'other':
                return 'Khác';
            default:
                return 'Không xác định';
        }
    }

    public function getWorkDaysLabelAttribute(): string
    {
        switch ($this->work_days) {
            case 'under_4':
                return 'Dưới 4 ngày';
            case '4_to_6':
                return '4-6 ngày';
            case '7_days':
                return '7 ngày';
            default:
                return 'Không xác định';
        }
    }

    public function getCvFileSizeFormattedAttribute(): string
    {
        if (!$this->cv_file_size) {
            return '';
        }

        $bytes = $this->cv_file_size;
        $units = ['B', 'KB', 'MB', 'GB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getCanWorkShiftsLabelAttribute(): string
    {
        switch ($this->can_work_shifts) {
            case 'yes':
                return 'Có';
            case 'no':
                return 'Không';
            default:
                return 'Chưa xác định';
        }
    }

    public function getCanWorkWeekendsLabelAttribute(): string
    {
        switch ($this->can_work_weekends) {
            case 'yes':
                return 'Có';
            case 'no':
                return 'Không';
            default:
                return 'Chưa xác định';
        }
    }

    public function getCanWorkHolidaysLabelAttribute(): string
    {
        switch ($this->can_work_holidays) {
            case 'yes':
                return 'Có';
            case 'no':
                return 'Không';
            default:
                return 'Chưa xác định';
        }
    }

    public function getCommitmentDurationLabelAttribute(): string
    {
        switch ($this->commitment_duration) {
            case 'under_3_months':
                return 'Dưới 3 tháng';
            case '3_to_6_months':
                return '3 - 6 tháng';
            case 'over_6_months':
                return 'Trên 6 tháng';
            default:
                return 'Chưa xác định';
        }
    }
}

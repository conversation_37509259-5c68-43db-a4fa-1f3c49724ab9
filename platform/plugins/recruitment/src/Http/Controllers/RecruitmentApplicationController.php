<?php

namespace Bo<PERSON><PERSON>\Recruitment\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\RecruitmentApplicationForm;
use Botble\Recruitment\Exports\RecruitmentApplicationExport;
use Botble\Recruitment\Models\RecruitmentApplication;
use Botble\Recruitment\Tables\RecruitmentApplicationTable;
use Botble\Recruitment\Enums\RecruitmentApplicationStatusEnum;
use Maatwebsite\Excel\Facades\Excel;
use Exception;
use Illuminate\Http\Request;
use Botble\Media\Facades\RvMedia;

class RecruitmentApplicationController extends BaseController
{
    public function __construct()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/recruitment::recruitment.applications'), route('recruitment-applications.index'));
    }

    public function index(RecruitmentApplicationTable $table)
    {
        $this->pageTitle(trans('plugins/recruitment::recruitment.applications'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/recruitment::recruitment.create_application'));

        return RecruitmentApplicationForm::create()->renderForm();
    }

    public function edit(RecruitmentApplication $application, Request $request)
    {
        event(new BeforeEditContentEvent($request, $application));

        $this->pageTitle(trans('plugins/recruitment::recruitment.edit_application', ['name' => $application->full_name]));

        return RecruitmentApplicationForm::createFromModel($application)->renderForm();
    }

    public function store(Request $request, BaseHttpResponse $response)
    {
        $application = RecruitmentApplication::create($request->input());

        event(new CreatedContentEvent(RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME, $request, $application));

        return $response
            ->setPreviousUrl(route('recruitment-applications.index'))
            ->setNextUrl(route('recruitment-applications.edit', $application->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function show(RecruitmentApplication $application, Request $request)
    {
        event(new BeforeEditContentEvent($request, $application));

        $this->pageTitle(trans('plugins/recruitment::recruitment.view_application', ['name' => $application->full_name]));

        return RecruitmentApplicationForm::createFromModel($application)->renderForm();
    }

    public function update(RecruitmentApplication $application, Request $request, BaseHttpResponse $response)
    {
        $application->fill($request->input());
        $application->save();

        event(new UpdatedContentEvent(RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME, $request, $application));

        return $response
            ->setPreviousUrl(route('recruitment-applications.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(RecruitmentApplication $application, Request $request, BaseHttpResponse $response)
    {
        try {
            $application->delete();

            event(new DeletedContentEvent(RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME, $request, $application));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function downloadCv(RecruitmentApplication $application)
    {
        if (!$application->cv_file_path) {
            abort(404, 'File CV không tồn tại');
        }

        // Get the real path from RvMedia
        $realPath = RvMedia::getRealPath($application->cv_file_path);

        if (!$realPath || !file_exists($realPath)) {
            abort(404, 'File CV không tồn tại');
        }

        return response()->download(
            $realPath,
            $application->cv_file_name ?: 'cv.pdf'
        );
    }

    public function export(Request $request)
    {
        // Debug: Log all request parameters
        \Log::info('Export request parameters:', $request->all());

        // Start with base query
        $query = RecruitmentApplication::query()
            ->select([
                'id',
                'full_name',
                'recruitment_name',
                'email',
                'phone',
                'gender',
                'birthday',
                'location_province_1',
                'location_district_1',
                'location_province_2',
                'location_district_2',
                'location_province_3',
                'location_district_3',
                'old_employee_code',
                'referral_code',
                'newsletter',
                'can_work_shifts',
                'can_work_weekends',
                'can_work_holidays',
                'work_days',
                'commitment_duration',
                'cv_file_path',
                'cv_file_name',
                'cv_file_size',
                'status',
                'notes',
                'created_at',
                'updated_at',
            ])
            ->with([
                'locationProvince1',
                'locationDistrict1',
                'locationProvince2',
                'locationDistrict2',
                'locationProvince3',
                'locationDistrict3',
                'recruitment'
            ]);

        // Apply filters manually
        $filterColumns = $request->input('filter_columns', []);

        if (!empty($filterColumns)) {
            foreach ($filterColumns as $index => $column) {
                $operator = $request->input("filter_operators.{$index}", '=');
                $value = $request->input("filter_values.{$index}", '');

                \Log::info("Processing filter - Column: {$column}, Operator: {$operator}, Value: {$value}");

                if (!empty($value) && !empty($column)) {
                    switch ($column) {
                        case 'status':
                            // Handle enum status values
                            $query->where('status', $operator, $value);
                            break;
                        case 'location_province_1':
                            $query->where('location_province_1', $operator, $value);
                            break;
                        case 'location_district_1':
                            $query->where('location_district_1', $operator, $value);
                            break;
                        case 'gender':
                            $query->where('gender', $operator, $value);
                            break;
                    }
                }
            }
        }

        \Log::info('Final Query SQL:', [$query->toSql()]);
        \Log::info('Final Query bindings:', $query->getBindings());

        // Create export with the filtered query
        $export = new RecruitmentApplicationExport($query);

        return Excel::download($export, 'recruitment_applications_' . date('Y-m-d_H-i-s') . '.xlsx');
    }
}

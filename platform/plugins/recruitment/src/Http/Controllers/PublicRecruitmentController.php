<?php

namespace Botble\Recruitment\Http\Controllers;

use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Facades\EmailHandler;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Recruitment\Enums\RecruitmentApplicationStatusEnum;
use Bo<PERSON>ble\Recruitment\Events\SentRecruitmentApplicationEvent;
use Botble\Recruitment\Models\District;
use Botble\Recruitment\Models\Province;
use Botble\Recruitment\Models\Recruitment;
use Botble\Recruitment\Models\RecruitmentApplication;
use Botble\Recruitment\Models\RecruitmentCategory;
use Botble\Recruitment\Models\WorkType;
use Botble\Recruitment\Http\Requests\RecruitmentApplicationRequest;
use Botble\Recruitment\Forms\Fronts\RecruitmentApplicationForm;
use Botble\Theme\Facades\Theme;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Botble\Language\Facades\Language;
use Botble\Media\Facades\RvMedia;
use Illuminate\Support\Arr;

class PublicRecruitmentController extends Controller
{
    public function getRecruitments(Request $request)
    {
        $query = Recruitment::query();
        $refLang = $request->input('ref_lang', Language::getCurrentLocaleCode());

        // Convert 'en' to 'en_US' if needed
        if ($refLang === 'en') {
            $refLang = 'en_US';
        }

        $query->where('status', BaseStatusEnum::PUBLISHED);

        // Filter by category
        if ($request->has('category')) {
            $categorySlug = $request->input('category');
            $categoryIds = RecruitmentCategory::where('slug', $categorySlug)->pluck('id')->toArray();
            $query->whereHas('categories', function ($q) use ($categoryIds) {
                $q->whereIn('recruitment_categories.id', $categoryIds);
            });
        }

        // Filter by work type
        if ($request->has('work_type')) {
            $workTypeSlug = $request->input('work_type');
            $workTypeIds = WorkType::where('slug', $workTypeSlug)->pluck('id')->toArray();
            $query->whereHas('workTypes', function ($q) use ($workTypeIds) {
                $q->whereIn('recruitment_work_types.id', $workTypeIds);
            });
        }

        // Filter by province
        if ($request->has('province')) {
            $provinceSlug = $request->input('province');
            $provinceIds = Province::where('slug', $provinceSlug)->pluck('id')->toArray();
            $query->whereIn('province_id', $provinceIds);
        }

        // Filter by district
        if ($request->has('district')) {
            $districtSlug = $request->input('district');
            $districtIds = District::where('slug', $districtSlug)->pluck('id')->toArray();
            $query->whereIn('district_id', $districtIds);
        }

        $query->orderBy('created_at', 'desc');
        $query->with(['categories', 'workTypes', 'province', 'district', 'slugable']);

        $recruitments = $query->paginate(12)
            ->through(function ($recruitment) use ($refLang) {
                if ($refLang === 'en_US') {
                    $recruitment->url_en = url('en/recruitment/' . $recruitment->slugable->key);
                }
                return $recruitment;
            });

        if ($request->ajax()) {
            return response()->json([
                'html' => view('plugins/recruitment::partials.recruitment-list', compact('recruitments', 'refLang'))->render(),
                'last_page' => $recruitments->lastPage()
            ]);
        }

        return view('plugins/recruitment::partials.recruitment-list', compact('recruitments'));
    }

    public function getRecruitmentsByCategory(string $slug, Request $request, BaseHttpResponse $response)
    {
        $category = RecruitmentCategory::query()
            ->where([
                'slug' => $slug,
                'status' => BaseStatusEnum::PUBLISHED,
            ])
            ->firstOrFail();

        $recruitments = Recruitment::query()
            ->where([
                'status' => BaseStatusEnum::PUBLISHED,
            ])
            ->whereHas('categories', function ($query) use ($category) {
                $query->where('recruitment_categories.id', $category->id);
            })
            ->with(['categories', 'workTypes', 'province', 'district'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        Theme::breadcrumb()
            ->add(__('Home'), route('public.index'))
            ->add(__('Recruitment'), route('public.recruitments'))
            ->add($category->name, route('public.recruitment-category', $category->slug));

        return Theme::scope('recruitment.category', compact('category', 'recruitments'))->render();
    }

    public function getRecruitmentsByWorkType(string $slug, Request $request, BaseHttpResponse $response)
    {
        $workType = WorkType::query()
            ->where([
                'slug' => $slug,
                'status' => BaseStatusEnum::PUBLISHED,
            ])
            ->firstOrFail();

        $recruitments = Recruitment::query()
            ->where([
                'status' => BaseStatusEnum::PUBLISHED,
            ])
            ->whereHas('workTypes', function ($query) use ($workType) {
                $query->where('recruitment_work_types.id', $workType->id);
            })
            ->with(['categories', 'workTypes', 'province', 'district'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        Theme::breadcrumb()
            ->add(__('Home'), route('public.index'))
            ->add(__('Recruitment'), route('public.recruitments'))
            ->add($workType->name, route('public.recruitment-work-type', $workType->slug));

        return Theme::scope('recruitment.work-type', compact('workType', 'recruitments'))->render();
    }

    public function getAllRecruitments(Request $request)
    {
        $recruitments = Recruitment::query()
            ->wherePublished()
            ->with(['categories', 'workTypes', 'province', 'district'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        Theme::breadcrumb()
            ->add(__('Home'), route('public.index'))
            ->add(__('Recruitment'), route('public.recruitments'));

        return Theme::scope('recruitment.recruitments', compact('recruitments'))->render();
    }

    public function getRecruitment(string $slug, Request $request)
    {
        $recruitment = Recruitment::query()
            ->wherePublished()
            ->whereHas('slugable', function ($query) use ($slug) {
                $query->where('key', $slug);
            })
            ->with(['categories', 'workTypes', 'province', 'district'])
            ->firstOrFail();

        Theme::breadcrumb()
            ->add(__('Home'), route('public.index'))
            ->add(__('Recruitment'), route('public.recruitments'))
            ->add($recruitment->name, $recruitment->url);

        return Theme::scope('recruitment.recruitment', compact('recruitment'))->render();
    }

    public function getDistricts(Request $request)
    {
        $provinceId = $request->input('province_id');
        $language = $request->input('language', 'vi');

        if (!$provinceId) {
            return response()->json([]);
        }

        $currentLanguage = $language;
        if ($currentLanguage == 'en') {
            $currentLanguage = 'en_US';
        }

        $districts = District::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_districts.id')
                    ->where('language_meta.reference_type', District::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->where('recruitment_districts.province_id', $provinceId)
            ->where('recruitment_districts.status', BaseStatusEnum::PUBLISHED)
            ->select('recruitment_districts.id', 'recruitment_districts.name')
            ->orderBy('recruitment_districts.order')
            ->orderBy('recruitment_districts.name')
            ->get();

        return response()->json($districts);
    }

    public function showApplicationForm(Request $request, $recruitmentId = null)
    {
        $recruitment = null;

        if ($recruitmentId) {
            $recruitment = Recruitment::query()
                ->where('id', $recruitmentId)
                ->where('status', BaseStatusEnum::PUBLISHED)
                ->first();

            if (!$recruitment) {
                abort(404);
            }
        }

        return Theme::scope('recruitment.application-form', compact('recruitment'))->render();
    }

    public function storeApplication(RecruitmentApplicationRequest $request, BaseHttpResponse $response)
    {
        try {
            $data = $request->validated();

            // Handle CV file upload using RvMedia
            if ($request->hasFile('cv_file')) {
                $file = $request->file('cv_file');

                // Upload file using RvMedia to 'recruitment/cv' folder
                $result = RvMedia::handleUpload($file, 0, 'recruitment/cv');

                if (!$result['error']) {
                    $uploadedFile = $result['data'];
                    $data['cv_file_path'] = $uploadedFile->url;
                    $data['cv_file_name'] = $file->getClientOriginalName();
                    $data['cv_file_size'] = $file->getSize();
                } else {
                    return $response
                        ->setError()
                        ->setMessage(__('Có lỗi khi tải lên file CV: ' . $result['message']));
                }
            }

            // Ensure status is set to NEW for new applications
            $data['status'] = RecruitmentApplicationStatusEnum::NEW;

            // Create application
            $application = RecruitmentApplication::create($data);

            // Trigger event for email sending
            event(new SentRecruitmentApplicationEvent($application));

            // Send email notifications
            $this->sendApplicationEmails($application);

            return $response->setMessage(__('Đơn ứng tuyển của bạn đã được gửi thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.'));

        } catch (\Exception $e) {
            \Log::error('Recruitment application submission error: ' . $e->getMessage());

            return $response
                ->setError()
                ->setMessage(__('Có lỗi xảy ra khi gửi đơn ứng tuyển. Vui lòng thử lại sau.'));
        }
    }

    protected function sendApplicationEmails(RecruitmentApplication $application)
    {
        try {
            // Get recruitment details
            $recruitment = $application->recruitment;

            // Get location details
            $province = $application->locationProvince1;
            $district = $application->locationDistrict1;

            // Prepare email variables
            $emailVariables = [
                'applicant_name' => $application->full_name,
                'applicant_email' => $application->email,
                'applicant_phone' => $application->phone,
                'applicant_birthday' => $application->birthday ? $application->birthday->format('d/m/Y') : '',
                'applicant_gender' => $application->gender ? __('plugins/recruitment::recruitment.gender_options.' . $application->gender) : '',
                'recruitment_name' => $application->recruitment_name ?: ($recruitment ? $recruitment->name : ''),
                'recruitment_url' => $recruitment ? $recruitment->url : '',
                'location_province_1' => $province ? $province->name : '',
                'location_district_1' => $district ? $district->name : '',
                'old_employee_code' => $application->old_employee_code ?: '',
                'referral_code' => $application->referral_code ?: '',
                'cv_file_name' => $application->cv_file_name ?: '',
                'cv_download_url' => $application->id ? route('recruitment-applications.download-cv', $application->id) : '',
                'application_date' => $application->created_at->format('d/m/Y H:i'),
                'application_status' => $application->status ? $application->status->label() : __('plugins/recruitment::recruitment.application_statuses.new'),
                'admin_url' => url('admin/recruitment-applications/' . $application->id),
                'company_name' => setting('site_title', config('app.name')),
                'contact_email' => setting('admin_email', config('mail.from.address')),
            ];

            // Get admin emails for notification
            $adminEmails = setting('admin_email');
            if (!$adminEmails) {
                $adminEmails = config('mail.from.address');
            }

            // Convert to array if it's a string
            if (is_string($adminEmails)) {
                $adminEmails = [$adminEmails];
            }

            // Send notification email to admin
            $emailHandler = EmailHandler::setModule(RECRUITMENT_MODULE_SCREEN_NAME)
                ->setVariableValues($emailVariables);

            $emailHandler->sendUsingTemplate('application-notice', $adminEmails);

            // Send confirmation email to applicant
            $emailHandler->sendUsingTemplate('application-confirmation', $application->email);

        } catch (\Exception $e) {
            \Log::error('Failed to send recruitment application emails: ' . $e->getMessage());
        }
    }
}

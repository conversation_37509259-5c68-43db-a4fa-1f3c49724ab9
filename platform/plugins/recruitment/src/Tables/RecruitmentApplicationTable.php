<?php

namespace Bo<PERSON>ble\Recruitment\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Recruitment\Exports\RecruitmentApplicationExport;
use <PERSON><PERSON><PERSON>\Recruitment\Models\RecruitmentApplication;
use Bo<PERSON>ble\Recruitment\Enums\RecruitmentApplicationStatusEnum;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Base\Facades\Assets;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\Columns\Column;
use Botble\Table\HeaderActions\HeaderAction;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class RecruitmentApplicationTable extends TableAbstract
{
    public function setup(): void
    {
        Assets::addScripts(['recruitment-applications']);

        $this
            ->model(RecruitmentApplication::class)
            ->addHeaderAction(
                HeaderAction::make('export')
                    ->label(trans('core/base::tables.export'))
                    ->icon('ti ti-download')
                    ->color('success')
                    ->url('javascript:;')
                    ->attributes([
                        'class' => 'export-application-button',
                    ])
            )
            ->addActions([
                EditAction::make()
                    ->route('recruitment-applications.edit'),
                DeleteAction::make()
                    ->route('recruitment-applications.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('full_name', function (RecruitmentApplication $item) {
                if (! $this->hasPermission('recruitment-applications.edit')) {
                    return BaseHelper::clean($item->full_name);
                }
                return Html::link(route('recruitment-applications.edit', $item->getKey()), BaseHelper::clean($item->full_name));
            })
            ->editColumn('status', function (RecruitmentApplication $item) {
                return $item->status->toHtml();
            })
            ->editColumn('gender', function (RecruitmentApplication $item) {
                return $item->gender_label;
            })
            ->editColumn('birthday', function (RecruitmentApplication $item) {
                return $item->birthday ? BaseHelper::formatDate($item->birthday) : null;
            })
            ->editColumn('location_province_1', function (RecruitmentApplication $item) {
                return $item->locationProvince1 ? $item->locationProvince1->name : null;
            })
            ->editColumn('location_district_1', function (RecruitmentApplication $item) {
                return $item->locationDistrict1 ? $item->locationDistrict1->name : null;
            })
            ->rawColumns(['status']);

        return $this->toJson($data);
    }

    public function query()
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'full_name',
                'recruitment_name',
                'email',
                'phone',
                'gender',
                'birthday',
                'location_province_1',
                'location_district_1',
                'status',
                'created_at',
            ])
            ->with([
                'locationProvince1',
                'locationDistrict1',
                'locationProvince2',
                'locationDistrict2',
                'locationProvince3',
                'locationDistrict3',
                'recruitment'
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            NameColumn::make('full_name')
                ->title(trans('plugins/recruitment::recruitment.full_name')),
            Column::make('recruitment_name')
                ->title(trans('plugins/recruitment::recruitment.position'))
                ->sortable(false),
            Column::make('email')
                ->title(trans('plugins/recruitment::recruitment.email'))
                ->sortable(false),
            Column::make('phone')
                ->title(trans('plugins/recruitment::recruitment.phone'))
                ->sortable(false),
            Column::make('gender')
                ->title(trans('plugins/recruitment::recruitment.gender'))
                ->sortable(false),
            Column::make('birthday')
                ->title(trans('plugins/recruitment::recruitment.birthday'))
                ->sortable(false),
            Column::make('location_province_1')
                ->title(trans('plugins/recruitment::recruitment.province'))
                ->sortable(false),
            Column::make('location_district_1')
                ->title(trans('plugins/recruitment::recruitment.district'))
                ->sortable(false),
            Column::make('status')
                ->title(trans('core/base::tables.status'))
                ->sortable(false),
            CreatedAtColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('recruitment-applications.create'), 'recruitment-applications.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('recruitment-applications.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => RecruitmentApplicationStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', RecruitmentApplicationStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type' => 'datePicker',
            ],
        ];
    }

    public function getFilters(): array
    {
        return [
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => ['' => trans('core/table::table.all')] + RecruitmentApplicationStatusEnum::labels(),
            ],
            'location_province_1' => [
                'title' => trans('plugins/recruitment::recruitment.province'),
                'type' => 'select',
                'choices' => ['' => trans('core/table::table.all')] +
                    \Botble\Recruitment\Models\Province::query()
                        ->where('status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray(),
            ],
            'location_district_1' => [
                'title' => trans('plugins/recruitment::recruitment.district'),
                'type' => 'select',
                'choices' => ['' => trans('core/table::table.all')] +
                    \Botble\Recruitment\Models\District::query()
                        ->where('status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray(),
            ],
            'gender' => [
                'title' => trans('plugins/recruitment::recruitment.gender'),
                'type' => 'select',
                'choices' => [
                    '' => trans('core/table::table.all'),
                    'male' => trans('plugins/recruitment::recruitment.gender_male'),
                    'female' => trans('plugins/recruitment::recruitment.gender_female'),
                    'other' => trans('plugins/recruitment::recruitment.gender_other'),
                ],
            ],
        ];
    }

    public function getDefaultButtons(): array
    {
        return [
            'reload',
        ];
    }

    public function applyFilterCondition($query, string $key, string $operator, ?string $value)
    {
        \Log::info("Table applyFilterCondition - Key: {$key}, Operator: {$operator}, Value: {$value}");

        if (empty($value)) {
            return $query;
        }

        switch ($key) {
            case 'status':
                // Handle enum status values - value is already the enum string value
                return $query->where('status', $operator, $value);
            case 'location_province_1':
                return $query->where('location_province_1', $operator, $value);
            case 'location_district_1':
                return $query->where('location_district_1', $operator, $value);
            case 'gender':
                return $query->where('gender', $operator, $value);
            default:
                // Use parent method for other columns
                return parent::applyFilterCondition($query, $key, $operator, $value);
        }
    }
}

<?php

namespace Botble\Recruitment\Tables\Actions;

use Botble\Table\Actions\Action;

class ExportApplicationsAction extends Action
{
    public static function make(string $name = 'export'): static
    {
        return parent::make($name)
            ->label(trans('core/base::tables.export'))
            ->icon('ti ti-download')
            ->color('success')
            ->route('recruitment-applications.export');
    }
}

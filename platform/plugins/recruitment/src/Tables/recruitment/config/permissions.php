<?php

return [
    [
        'name' => 'Recruitments',
        'flag' => 'recruitment.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'recruitment.create',
        'parent_flag' => 'recruitment.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'recruitment.edit',
        'parent_flag' => 'recruitment.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'recruitment.destroy',
        'parent_flag' => 'recruitment.index',
    ],

    [
        'name' => 'Recruitment Categories',
        'flag' => 'recruitment-categories.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'recruitment-categories.create',
        'parent_flag' => 'recruitment-categories.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'recruitment-categories.edit',
        'parent_flag' => 'recruitment-categories.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'recruitment-categories.destroy',
        'parent_flag' => 'recruitment-categories.index',
    ],

    [
        'name' => 'Recruitment Tags',
        'flag' => 'recruitment-tags.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'recruitment-tags.create',
        'parent_flag' => 'recruitment-tags.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'recruitment-tags.edit',
        'parent_flag' => 'recruitment-tags.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'recruitment-tags.destroy',
        'parent_flag' => 'recruitment-tags.index',
    ],

    [
        'name' => 'Work Types',
        'flag' => 'recruitment-work-types.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'recruitment-work-types.create',
        'parent_flag' => 'recruitment-work-types.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'recruitment-work-types.edit',
        'parent_flag' => 'recruitment-work-types.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'recruitment-work-types.destroy',
        'parent_flag' => 'recruitment-work-types.index',
    ],

    [
        'name' => 'Provinces',
        'flag' => 'recruitment-provinces.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'recruitment-provinces.create',
        'parent_flag' => 'recruitment-provinces.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'recruitment-provinces.edit',
        'parent_flag' => 'recruitment-provinces.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'recruitment-provinces.destroy',
        'parent_flag' => 'recruitment-provinces.index',
    ],

    [
        'name' => 'Districts',
        'flag' => 'recruitment-districts.index',
    ],
    [
        'name' => 'Create',
        'flag' => 'recruitment-districts.create',
        'parent_flag' => 'recruitment-districts.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'recruitment-districts.edit',
        'parent_flag' => 'recruitment-districts.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'recruitment-districts.destroy',
        'parent_flag' => 'recruitment-districts.index',
    ],

    [
        'name' => 'Recruitment Applications',
        'flag' => 'recruitment-applications.index',
    ],
    [
        'name' => 'View',
        'flag' => 'recruitment-applications.show',
        'parent_flag' => 'recruitment-applications.index',
    ],
    [
        'name' => 'Edit',
        'flag' => 'recruitment-applications.edit',
        'parent_flag' => 'recruitment-applications.index',
    ],
    [
        'name' => 'Delete',
        'flag' => 'recruitment-applications.destroy',
        'parent_flag' => 'recruitment-applications.index',
    ],
];

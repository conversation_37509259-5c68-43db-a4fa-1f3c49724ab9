<?php

use Bo<PERSON>ble\ACL\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (!Schema::hasTable('recruitment_categories')) {
            Schema::create('recruitment_categories', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('description', 400)->nullable();
                $table->string('status', 60)->default('published');
                $table->integer('order')->unsigned()->default(0);
                $table->tinyInteger('is_default')->unsigned()->default(0);
                $table->timestamps();
            });
        }

        if (!Schema::hasTable('recruitment_work_types')) {
            Schema::create('recruitment_work_types', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('description', 400)->nullable();
                $table->string('status', 60)->default('published');
                $table->integer('order')->unsigned()->default(0);
                $table->timestamps();
            });
        }

        if (!Schema::hasTable('recruitment_provinces')) {
            Schema::create('recruitment_provinces', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('slug')->unique();
                $table->integer('order')->unsigned()->default(0);
                $table->tinyInteger('is_default')->unsigned()->default(0);
                $table->string('status', 60)->default('published');
                $table->timestamps();
            });
        }

        if (!Schema::hasTable('recruitment_districts')) {
            Schema::create('recruitment_districts', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->foreignId('province_id');
                $table->string('slug')->unique();
                $table->integer('order')->unsigned()->default(0);
                $table->tinyInteger('is_default')->unsigned()->default(0);
                $table->string('status', 60)->default('published');
                $table->timestamps();

                $table->foreign('province_id')->references('id')->on('recruitment_provinces')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('recruitments')) {
            Schema::create('recruitments', function (Blueprint $table) {
                $table->id();
                $table->string('name');
                $table->string('description', 400)->nullable();
                $table->longText('content')->nullable();
                $table->longText('requirements')->nullable();
                $table->longText('benefits')->nullable();
                $table->string('status', 60)->default('published');
                $table->foreignId('province_id')->nullable();
                $table->foreignId('district_id')->nullable();
                $table->integer('quantity')->unsigned()->default(1);
                $table->string('salary')->nullable();
                $table->date('deadline')->nullable();
                $table->string('image')->nullable();
                $table->tinyInteger('is_featured')->unsigned()->default(0);
                $table->integer('views')->unsigned()->default(0);
                $table->foreignId('author_id')->nullable();
                $table->string('author_type', 255)->default(addslashes(User::class));
                $table->timestamps();
            });
        }

        // Create pivot tables for many-to-many relationships
        if (!Schema::hasTable('recruitment_category')) {
            Schema::create('recruitment_category', function (Blueprint $table) {
                $table->foreignId('recruitment_id');
                $table->foreignId('category_id');

                $table->primary(['recruitment_id', 'category_id']);
                $table->foreign('recruitment_id')->references('id')->on('recruitments')->onDelete('cascade');
                $table->foreign('category_id')->references('id')->on('recruitment_categories')->onDelete('cascade');
            });
        }

        if (!Schema::hasTable('recruitment_work_type')) {
            Schema::create('recruitment_work_type', function (Blueprint $table) {
                $table->foreignId('recruitment_id');
                $table->foreignId('work_type_id');

                $table->primary(['recruitment_id', 'work_type_id']);
                $table->foreign('recruitment_id')->references('id')->on('recruitments')->onDelete('cascade');
                $table->foreign('work_type_id')->references('id')->on('recruitment_work_types')->onDelete('cascade');
            });
        }

        // Create translation tables
        if (!Schema::hasTable('recruitment_categories_translations')) {
            Schema::create('recruitment_categories_translations', function (Blueprint $table) {
                $table->string('lang_code');
                $table->foreignId('recruitment_categories_id');
                $table->string('name')->nullable();
                $table->string('description', 400)->nullable();

                $table->primary(['lang_code', 'recruitment_categories_id'], 'recruitment_categories_translations_primary');
            });
        }

        if (!Schema::hasTable('recruitment_work_types_translations')) {
            Schema::create('recruitment_work_types_translations', function (Blueprint $table) {
                $table->string('lang_code');
                $table->foreignId('recruitment_work_types_id');
                $table->string('name')->nullable();
                $table->string('description', 400)->nullable();

                $table->primary(['lang_code', 'recruitment_work_types_id'], 'recruitment_work_types_translations_primary');
            });
        }

        if (!Schema::hasTable('recruitments_translations')) {
            Schema::create('recruitments_translations', function (Blueprint $table) {
                $table->string('lang_code');
                $table->foreignId('recruitments_id');
                $table->string('name')->nullable();
                $table->string('description', 400)->nullable();
                $table->longText('content')->nullable();
                $table->longText('requirements')->nullable();
                $table->longText('benefits')->nullable();
                $table->string('salary')->nullable();

                $table->primary(['lang_code', 'recruitments_id'], 'recruitments_translations_primary');
            });
        }

        if (!Schema::hasTable('recruitment_provinces_translations')) {
            Schema::create('recruitment_provinces_translations', function (Blueprint $table) {
                $table->string('lang_code');
                $table->foreignId('recruitment_provinces_id');
                $table->string('name')->nullable();

                $table->primary(['lang_code', 'recruitment_provinces_id'], 'recruitment_provinces_translations_primary');
            });
        }

        if (!Schema::hasTable('recruitment_districts_translations')) {
            Schema::create('recruitment_districts_translations', function (Blueprint $table) {
                $table->string('lang_code');
                $table->foreignId('recruitment_districts_id');
                $table->string('name')->nullable();

                $table->primary(['lang_code', 'recruitment_districts_id'], 'recruitment_districts_translations_primary');
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('recruitments_translations');
        Schema::dropIfExists('recruitment_work_types_translations');
        Schema::dropIfExists('recruitment_categories_translations');
        Schema::dropIfExists('recruitment_provinces_translations');
        Schema::dropIfExists('recruitment_districts_translations');
        Schema::dropIfExists('recruitment_category');
        Schema::dropIfExists('recruitment_work_type');
        Schema::dropIfExists('recruitments');
        Schema::dropIfExists('recruitment_districts');
        Schema::dropIfExists('recruitment_provinces');
        Schema::dropIfExists('recruitment_work_types');
        Schema::dropIfExists('recruitment_categories');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('recruitment_applications')) {
            Schema::table('recruitment_applications', function (Blueprint $table) {
                // Add new fields for work preferences
                $table->enum('can_work_shifts', ['yes', 'no'])->nullable()->after('newsletter')->comment('Có thể làm ca xoay');
                $table->enum('can_work_weekends', ['yes', 'no'])->nullable()->after('can_work_shifts')->comment('Có thể làm cuối tuần');
                $table->enum('can_work_holidays', ['yes', 'no'])->nullable()->after('can_work_weekends')->comment('<PERSON><PERSON> thể làm ngày lễ/tết');
                $table->enum('commitment_duration', ['under_3_months', '3_to_6_months', 'over_6_months'])->nullable()->after('work_days')->comment('Thời gian gắn bó mong muốn');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('recruitment_applications')) {
            Schema::table('recruitment_applications', function (Blueprint $table) {
                $table->dropColumn([
                    'can_work_shifts',
                    'can_work_weekends', 
                    'can_work_holidays',
                    'commitment_duration'
                ]);
            });
        }
    }
};

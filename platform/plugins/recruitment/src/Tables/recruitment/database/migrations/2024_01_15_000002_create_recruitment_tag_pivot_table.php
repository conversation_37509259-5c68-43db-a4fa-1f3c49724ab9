<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('recruitment_tag_pivot', function (Blueprint $table) {
            $table->id();
            $table->foreignId('recruitment_id')->constrained('recruitments')->onDelete('cascade');
            $table->foreignId('tag_id')->constrained('recruitment_tags')->onDelete('cascade');
            $table->timestamps();

            $table->unique(['recruitment_id', 'tag_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('recruitment_tag_pivot');
    }
};

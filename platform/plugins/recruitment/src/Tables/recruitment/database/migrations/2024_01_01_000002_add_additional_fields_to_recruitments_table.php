<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('recruitments')) {
            Schema::table('recruitments', function (Blueprint $table) {
                $table->string('work_mode')->nullable()->after('salary'); // Chế độ làm việc
                $table->text('product_benefits')->nullable()->after('work_mode'); // Ưu đãi sản phẩm
                $table->string('business_revenue')->nullable()->after('product_benefits'); // Thương doanh số
                $table->string('working_hours')->nullable()->after('business_revenue'); // Thời gian làm việc
                $table->text('promotion_opportunities')->nullable()->after('working_hours'); // <PERSON><PERSON> hội thăng tiến
                $table->text('internal_bonding_programs')->nullable()->after('promotion_opportunities'); // Chương trình gắn kết nội bộ
                $table->string('job_level')->nullable()->after('internal_bonding_programs'); // Cấp bậc
                $table->text('health_checkups')->nullable()->after('job_level'); // Khám sức khỏe
            });
        }

        // Add translation fields for new columns
        if (Schema::hasTable('recruitments_translations')) {
            Schema::table('recruitments_translations', function (Blueprint $table) {
                $table->string('work_mode')->nullable()->after('salary');
                $table->text('product_benefits')->nullable()->after('work_mode');
                $table->string('business_revenue')->nullable()->after('product_benefits');
                $table->string('working_hours')->nullable()->after('business_revenue');
                $table->text('promotion_opportunities')->nullable()->after('working_hours');
                $table->text('internal_bonding_programs')->nullable()->after('promotion_opportunities');
                $table->string('job_level')->nullable()->after('internal_bonding_programs');
                $table->text('health_checkups')->nullable()->after('job_level');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('recruitments')) {
            Schema::table('recruitments', function (Blueprint $table) {
                $table->dropColumn([
                    'work_mode',
                    'product_benefits',
                    'business_revenue',
                    'working_hours',
                    'promotion_opportunities',
                    'internal_bonding_programs',
                    'job_level',
                    'health_checkups'
                ]);
            });
        }

        if (Schema::hasTable('recruitments_translations')) {
            Schema::table('recruitments_translations', function (Blueprint $table) {
                $table->dropColumn([
                    'work_mode',
                    'product_benefits',
                    'business_revenue',
                    'working_hours',
                    'promotion_opportunities',
                    'internal_bonding_programs',
                    'job_level',
                    'health_checkups'
                ]);
            });
        }
    }
};

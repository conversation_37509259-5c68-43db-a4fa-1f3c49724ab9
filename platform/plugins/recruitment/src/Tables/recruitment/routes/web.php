<?php

use <PERSON><PERSON><PERSON>\Base\Facades\AdminHelper;
use Bo<PERSON><PERSON>\Recruitment\Http\Controllers\RecruitmentController;
use <PERSON><PERSON><PERSON>\Recruitment\Http\Controllers\RecruitmentApplicationController;

use <PERSON><PERSON><PERSON>\Recruitment\Http\Controllers\RecruitmentCategoryController;
use Bo<PERSON>ble\Recruitment\Http\Controllers\RecruitmentTagController;
use Bo<PERSON>ble\Recruitment\Http\Controllers\WorkTypeController;
use Bo<PERSON>ble\Recruitment\Http\Controllers\ProvinceController;
use Botble\Recruitment\Http\Controllers\DistrictController;
use Bo<PERSON>ble\Recruitment\Http\Controllers\PublicRecruitmentController;
use Illuminate\Support\Facades\Route;

AdminHelper::registerRoutes(function () {
    Route::group(['prefix' => 'recruitments', 'as' => 'recruitment.'], function () {
        Route::resource('', RecruitmentController::class)->parameters(['' => 'recruitment']);
    });

    Route::group(['prefix' => 'recruitment-applications', 'as' => 'recruitment-applications.'], function () {
        Route::resource('', RecruitmentApplicationController::class)->parameters(['' => 'application']);
        Route::get('{application}/download-cv', [RecruitmentApplicationController::class, 'downloadCv'])->name('download-cv');
        Route::get('export', [RecruitmentApplicationController::class, 'export'])->name('export');
    });

    Route::group(['prefix' => 'recruitment-categories', 'as' => 'recruitment-categories.'], function () {
        Route::resource('', RecruitmentCategoryController::class)->parameters(['' => 'category']);
    });

    Route::group(['prefix' => 'recruitment-tags', 'as' => 'recruitment-tags.'], function () {
        Route::resource('', RecruitmentTagController::class)->parameters(['' => 'tag']);
    });

    Route::group(['prefix' => 'recruitment-work-types', 'as' => 'recruitment-work-types.'], function () {
        Route::resource('', WorkTypeController::class)->parameters(['' => 'workType']);
    });

    Route::group(['prefix' => 'recruitment-provinces', 'as' => 'recruitment-provinces.'], function () {
        Route::resource('', ProvinceController::class)->parameters(['' => 'province']);
    });

    Route::group(['prefix' => 'recruitment-districts', 'as' => 'recruitment-districts.'], function () {
        Route::resource('', DistrictController::class)->parameters(['' => 'district']);
    });

    Route::get('ajax/districts-by-province/{province_id}', [DistrictController::class, 'getDistrictsByProvince'])->name('ajax.recruitment.get-districts');
});

Route::group(['namespace' => 'Botble\Recruitment\Http\Controllers', 'middleware' => ['web', 'core']], function () {
    Route::group(['prefix' => 'ajax'], function () {
        Route::get('search-recruitments', [PublicRecruitmentController::class, 'getRecruitments'])->name('public.recruitment.search-recruitments');
        Route::get('districts', [PublicRecruitmentController::class, 'getDistricts'])->name('public.recruitment.get-districts');
    });

    Route::get('recruitment/application/{recruitmentId?}', [PublicRecruitmentController::class, 'showApplicationForm'])->name('public.recruitment.application.form');
    Route::post('recruitment/application', [PublicRecruitmentController::class, 'storeApplication'])->name('public.recruitment.application.store');

    Route::get('recruitments', [PublicRecruitmentController::class, 'getAllRecruitments'])
        ->name('public.recruitments');

    Route::get('recruitment/{slug}', [PublicRecruitmentController::class, 'getRecruitment'])
        ->name('public.recruitment');

    Route::group(['prefix' => 'recruitment-category'], function () {
        Route::get('{slug}', [PublicRecruitmentController::class, 'getRecruitmentsByCategory'])
            ->name('public.recruitment-category');
    });

    Route::group(['prefix' => 'recruitment-work-type'], function () {
        Route::get('{slug}', [PublicRecruitmentController::class, 'getRecruitmentsByWorkType'])
            ->name('public.recruitment-work-type');
    });
});

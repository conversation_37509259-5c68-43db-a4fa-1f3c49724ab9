<?php

namespace Bo<PERSON><PERSON>\Recruitment\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Media\Facades\RvMedia;
use <PERSON><PERSON>ble\Recruitment\Models\Recruitment;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class RecruitmentTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(Recruitment::class)
            ->addActions([
                EditAction::make()
                    ->route('recruitment.edit'),
                DeleteAction::make()
                    ->route('recruitment.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('image', function (Recruitment $item) {
                return Html::image(
                    RvMedia::getImageUrl($item->image, 'thumb', false, RvMedia::getDefaultImage()),
                    $item->name,
                    ['width' => 50]
                );
            })
            ->editColumn('name', function (Recruitment $item) {
                if (! $this->hasPermission('recruitment.edit')) {
                    return BaseHelper::clean($item->name);
                }
                return Html::link(route('recruitment.edit', $item->getKey()), BaseHelper::clean($item->name));
            })
            ->editColumn('categories', function (Recruitment $item) {
                return $item->categories->pluck('name')->implode(', ');
            })
            ->editColumn('work_types', function (Recruitment $item) {
                return $item->workTypes->pluck('name')->implode(', ');
            })
            ->editColumn('deadline', function (Recruitment $item) {
                return $item->deadline ? BaseHelper::formatDate($item->deadline) : null;
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'image',
                'created_at',
                'status',
                'deadline',
            ])
            ->with(['categories', 'workTypes']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            Column::make('image')
                ->title(trans('core/base::tables.image'))
                ->width(70),
            NameColumn::make(),
            Column::make('categories')
                ->title(trans('plugins/recruitment::recruitment.categories'))
                ->sortable(false),
            Column::make('work_types')
                ->title(trans('plugins/recruitment::recruitment.work_types'))
                ->sortable(false),
            Column::make('deadline')
                ->title(trans('plugins/recruitment::recruitment.deadline'))
                ->sortable(false),
            CreatedAtColumn::make(),
            StatusColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('recruitment.create'), 'recruitment.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('recruitment.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            'name' => [
                'title' => trans('core/base::tables.name'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type' => 'datePicker',
            ],
        ];
    }
}

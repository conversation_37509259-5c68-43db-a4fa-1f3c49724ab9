<?php

namespace Bo<PERSON><PERSON>\Recruitment\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Recruitment\Exports\RecruitmentApplicationExport;
use <PERSON><PERSON><PERSON>\Recruitment\Models\RecruitmentApplication;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\Columns\Column;
use Botble\Table\HeaderActions\HeaderAction;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class RecruitmentApplicationTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(RecruitmentApplication::class)
            ->addHeaderAction(
                HeaderAction::make('export')
                    ->label(trans('core/base::tables.export'))
                    ->icon('ti ti-download')
                    ->color('success')
                    ->route('recruitment-applications.export')
            )
            ->addActions([
                EditAction::make()
                    ->route('recruitment-applications.edit'),
                DeleteAction::make()
                    ->route('recruitment-applications.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('full_name', function (RecruitmentApplication $item) {
                if (! $this->hasPermission('recruitment-applications.edit')) {
                    return BaseHelper::clean($item->full_name);
                }
                return Html::link(route('recruitment-applications.edit', $item->getKey()), BaseHelper::clean($item->full_name));
            })
            ->editColumn('status', function (RecruitmentApplication $item) {
                $statusClass = 'secondary';
                switch ($item->status) {
                    case 'pending':
                        $statusClass = 'warning';
                        break;
                    case 'reviewing':
                        $statusClass = 'info';
                        break;
                    case 'approved':
                        $statusClass = 'success';
                        break;
                    case 'rejected':
                        $statusClass = 'danger';
                        break;
                }
                return '<span class="badge bg-' . $statusClass . '">' . $item->status_label . '</span>';
            })
            ->editColumn('gender', function (RecruitmentApplication $item) {
                return $item->gender_label;
            })
            ->editColumn('birthday', function (RecruitmentApplication $item) {
                return $item->birthday ? BaseHelper::formatDate($item->birthday) : null;
            })
            ->editColumn('location_province_1', function (RecruitmentApplication $item) {
                return $item->locationProvince1 ? $item->locationProvince1->name : null;
            })
            ->editColumn('location_district_1', function (RecruitmentApplication $item) {
                return $item->locationDistrict1 ? $item->locationDistrict1->name : null;
            })
            ->rawColumns(['status']);

        return $this->toJson($data);
    }

    public function query()
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'full_name',
                'recruitment_name',
                'email',
                'phone',
                'gender',
                'birthday',
                'location_province_1',
                'location_district_1',
                'status',
                'created_at',
            ])
            ->with(['locationProvince1', 'locationDistrict1', 'recruitment']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            NameColumn::make('full_name')
                ->title(trans('plugins/recruitment::recruitment.full_name')),
            Column::make('recruitment_name')
                ->title(trans('plugins/recruitment::recruitment.position'))
                ->sortable(false),
            Column::make('email')
                ->title(trans('plugins/recruitment::recruitment.email'))
                ->sortable(false),
            Column::make('phone')
                ->title(trans('plugins/recruitment::recruitment.phone'))
                ->sortable(false),
            Column::make('gender')
                ->title(trans('plugins/recruitment::recruitment.gender'))
                ->sortable(false),
            Column::make('birthday')
                ->title(trans('plugins/recruitment::recruitment.birthday'))
                ->sortable(false),
            Column::make('location_province_1')
                ->title(trans('plugins/recruitment::recruitment.province'))
                ->sortable(false),
            Column::make('location_district_1')
                ->title(trans('plugins/recruitment::recruitment.district'))
                ->sortable(false),
            Column::make('status')
                ->title(trans('core/base::tables.status'))
                ->sortable(false),
            CreatedAtColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('recruitment-applications.create'), 'recruitment-applications.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('recruitment-applications.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => [
                    'pending' => trans('plugins/recruitment::recruitment.status_pending'),
                    'reviewing' => trans('plugins/recruitment::recruitment.status_reviewing'),
                    'approved' => trans('plugins/recruitment::recruitment.status_approved'),
                    'rejected' => trans('plugins/recruitment::recruitment.status_rejected'),
                ],
                'validate' => 'required|in:pending,reviewing,approved,rejected',
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type' => 'datePicker',
            ],
        ];
    }

    public function getFilters(): array
    {
        return [
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => [
                    '' => trans('core/table::table.all'),
                    'pending' => trans('plugins/recruitment::recruitment.status_pending'),
                    'reviewing' => trans('plugins/recruitment::recruitment.status_reviewing'),
                    'approved' => trans('plugins/recruitment::recruitment.status_approved'),
                    'rejected' => trans('plugins/recruitment::recruitment.status_rejected'),
                ],
            ],
            'location_province_1' => [
                'title' => trans('plugins/recruitment::recruitment.province'),
                'type' => 'select',
                'choices' => ['' => trans('core/table::table.all')] +
                    \Botble\Recruitment\Models\Province::query()
                        ->where('status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray(),
            ],
            'location_district_1' => [
                'title' => trans('plugins/recruitment::recruitment.district'),
                'type' => 'select',
                'choices' => ['' => trans('core/table::table.all')] +
                    \Botble\Recruitment\Models\District::query()
                        ->where('status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
                        ->orderBy('name')
                        ->pluck('name', 'id')
                        ->toArray(),
            ],
            'gender' => [
                'title' => trans('plugins/recruitment::recruitment.gender'),
                'type' => 'select',
                'choices' => [
                    '' => trans('core/table::table.all'),
                    'male' => trans('plugins/recruitment::recruitment.gender_male'),
                    'female' => trans('plugins/recruitment::recruitment.gender_female'),
                    'other' => trans('plugins/recruitment::recruitment.gender_other'),
                ],
            ],
        ];
    }

    public function getDefaultButtons(): array
    {
        return [
            'reload',
        ];
    }
}

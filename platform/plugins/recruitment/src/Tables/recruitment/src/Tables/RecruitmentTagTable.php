<?php

namespace Bo<PERSON><PERSON>\Recruitment\Tables;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Recruitment\Models\RecruitmentTag;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class RecruitmentTagTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(RecruitmentTag::class)
            ->addActions([
                EditAction::make()
                    ->route('recruitment-tags.edit'),
                DeleteAction::make()
                    ->route('recruitment-tags.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function (RecruitmentTag $item) {
                if (! $this->hasPermission('recruitment-tags.edit')) {
                    return BaseHelper::clean($item->name);
                }

                return Html::link(route('recruitment-tags.edit', $item->getKey()), BaseHelper::clean($item->name));
            })
            ->editColumn('color', function (RecruitmentTag $item) {
                return Html::tag('span', '', [
                    'style' => 'display: inline-block; width: 20px; height: 20px; background-color: ' . $item->color . '; border-radius: 3px; border: 1px solid #ddd;'
                ]) . ' ' . $item->color;
            });

        return $this->toJson($data);
    }

    public function query()
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'color',
                'status',
                'created_at',
                'order',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            NameColumn::make()->route('recruitment-tags.edit'),
            Column::make('color')
                ->title(trans('plugins/recruitment::recruitment.tag_color'))
                ->alignLeft(),
            Column::make('order')
                ->title(trans('core/base::forms.order'))
                ->alignLeft(),
            StatusColumn::make(),
            CreatedAtColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('recruitment-tags.create'), 'recruitment-tags.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('recruitment-tags.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            'name' => [
                'title' => trans('core/base::tables.name'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type' => 'datePicker',
            ],
        ];
    }
}

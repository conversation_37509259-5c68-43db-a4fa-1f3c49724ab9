<?php

namespace Bo<PERSON><PERSON>\Recruitment\Tables;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Facades\Html;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Recruitment\Models\District;
use Botble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class DistrictTable extends TableAbstract
{
    public function setup(): void
    {
        $this
            ->model(District::class)
            ->addActions([
                EditAction::make()
                    ->route('recruitment-districts.edit'),
                DeleteAction::make()
                    ->route('recruitment-districts.destroy'),
            ]);
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('name', function (District $item) {
                if (! $this->hasPermission('recruitment-districts.edit')) {
                    return BaseHelper::clean($item->name);
                }
                return Html::link(route('recruitment-districts.edit', $item->getKey()), BaseHelper::clean($item->name));
            })
            ->editColumn('province_id', function (District $item) {
                return $item->province ? $item->province->name : null;
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this
            ->getModel()
            ->query()
            ->select([
                'id',
                'name',
                'province_id',
                'created_at',
                'status',
                'order',
            ])
            ->with(['province']);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            NameColumn::make(),
            Column::make('province_id')
                ->title(trans('plugins/recruitment::recruitment.province'))
                ->sortable(false),
            Column::make('order')
                ->title(trans('core/base::tables.order'))
                ->width(100),
            CreatedAtColumn::make(),
            StatusColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('recruitment-districts.create'), 'recruitment-districts.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('recruitment-districts.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            'name' => [
                'title' => trans('core/base::tables.name'),
                'type' => 'text',
                'validate' => 'required|max:120',
            ],
            'status' => [
                'title' => trans('core/base::tables.status'),
                'type' => 'select',
                'choices' => BaseStatusEnum::labels(),
                'validate' => 'required|in:' . implode(',', BaseStatusEnum::values()),
            ],
            'created_at' => [
                'title' => trans('core/base::tables.created_at'),
                'type' => 'datePicker',
            ],
        ];
    }
}

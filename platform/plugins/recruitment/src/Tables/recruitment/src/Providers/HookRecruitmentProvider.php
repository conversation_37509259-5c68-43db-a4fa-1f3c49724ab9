<?php

namespace Bo<PERSON><PERSON>\Recruitment\Providers;

use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use <PERSON><PERSON>ble\Recruitment\Models\Recruitment;
use <PERSON><PERSON>ble\Recruitment\Models\RecruitmentCategory;
use <PERSON><PERSON><PERSON>\Recruitment\Models\Province;
use <PERSON><PERSON>ble\Recruitment\Models\District;
use Bo<PERSON>ble\Recruitment\Models\WorkType;
use Bo<PERSON>ble\Recruitment\Services\RecruitmentService;
use Bo<PERSON>ble\Slug\Models\Slug;
use Botble\Page\Models\Page;
use Bo<PERSON>ble\Theme\Events\RenderingThemeOptionSettings;
use Botble\Base\Enums\BaseStatusEnum;
use Botble\Theme\Facades\Theme;
use Botble\Language\Facades\Language;
use Illuminate\Support\Facades\DB;

class HookRecruitmentProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(BASE_FILTER_PUBLIC_SINGLE_DATA, [$this, 'handleSingleRecruitmentView'], 2);

        $this->app['events']->listen(RenderingThemeOptionSettings::class, function () {
            add_action(RENDERING_THEME_OPTIONS_PAGE, [$this, 'addThemeOptionsRecruitment'], 36);
        });

        if (defined('PAGE_MODULE_SCREEN_NAME')) {
            add_filter(PAGE_FILTER_FRONT_PAGE_CONTENT, [$this, 'renderRecruitmentPage'], 2, 2);
            add_filter(PAGE_FILTER_FRONT_PAGE_CONTENT, [$this, 'renderRecruitmentPageApplication'], 2, 2);

        }
    }

    public function handleSingleRecruitmentView($slug)
    {
        return (new RecruitmentService())->handleFrontRoutesRecruitment($slug);
    }

    public function addThemeOptionsRecruitment(): void
    {
        $pages = Page::query()
            ->wherePublished()
            ->pluck('name', 'id')
            ->all();

        theme_option()
            ->setSection([
                'title' => 'Tuyển dụng',
                'id' => 'opt-text-subsection-recruitment',
                'subsection' => true,
                'icon' => 'ti ti-briefcase',
            ])
            ->setField([
                'id' => 'recruitment_page_id',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'customSelect',
                'label' => __('Trang danh sách tuyển dụng'),
                'attributes' => [
                    'name' => 'recruitment_page_id',
                    'list' => [0 => __('Select')] + $pages,
                    'value' => '',
                    'options' => [
                        'class' => 'form-control',
                    ],
                ],
            ])
            ->setField([
                'id' => 'recruitment_application_page_id',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'customSelect',
                'label' => __('Trang ứng tuyển'),
                'attributes' => [
                    'name' => 'recruitment_application_page_id',
                    'list' => [0 => __('Select')] + $pages,
                    'value' => '',
                    'options' => [
                        'class' => 'form-control',
                    ],
                ],
            ])
            ->setField([
                'id' => 'recruitment_form_background',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'mediaImage',
                'label' => __('Ảnh nền form tìm kiếm tuyển dụng'),
                'attributes' => [
                    'name' => 'recruitment_form_background',
                    'value' => null,
                ],
            ])
            ->setField([
                'id' => 'recruitment_form_content',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'editor',
                'label' => __('Nội dung lưu ý'),
                'attributes' => [
                    'name' => 'recruitment_form_content',
                    'value' => null,
                ],
            ])
            ->setField([
                'id' => 'recruitment_form_note',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'textarea',
                'label' => __('Lưu ý chính'),
                'attributes' => [
                    'name' => 'recruitment_form_note',
                    'value' => null,
                    'options' => [
                        'class' => 'form-control',
                        // 'data-counter' => 120,
                    ],
                ],
            ])
            ->setField([
                'id' => 'recruitment_form_image_thankyou',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'mediaImage',
                'label' => __('Ảnh nền popup thành công'),
                'attributes' => [
                    'name' => 'recruitment_form_image_thankyou',
                    'value' => null,
                ],
            ])
            ->setField([
                'id' => 'recruitment_form_title_thankyou',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'text',
                'label' => __('Tiêu đề popup thành công'),
                'attributes' => [
                    'name' => 'recruitment_form_title_thankyou',
                    'value' => null,
                    'options' => [
                        'class' => 'form-control',
                        // 'data-counter' => 120,
                    ],
                ],
            ])
            ->setField([
                'id' => 'recruitment_form_content_thankyou',
                'section_id' => 'opt-text-subsection-recruitment',
                'type' => 'editor',
                'label' => __('Nội dung popup thành công'),
                'attributes' => [
                    'name' => 'recruitment_form_content_thankyou',
                    'value' => null,
                    'options' => [
                        'class' => 'form-control',
                        // 'data-counter' => 120,
                    ],
                ],
            ]);
    }

    public function renderRecruitmentPageApplication(?string $content, Page $page): ?string
    {
        if ($page->getKey() == $this->getRecruitmentApplicationPageId()) {
            $recruitmentId = request()->input('recruitment_id');
            $recruitment = null;
            
            if ($recruitmentId) {
                $currentLanguage = Language::getCurrentLocaleCode();
                if ($currentLanguage == 'en') {
                    $currentLanguage = 'en_US';
                }
                
                $recruitment = Recruitment::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitments.id')
                            ->where('language_meta.reference_type', Recruitment::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where('recruitments.id', $recruitmentId)
                    ->where('recruitments.status', BaseStatusEnum::PUBLISHED)
                    ->select('recruitments.*')
                    ->first();
            }
            
            return view('plugins/recruitment::themes.recruitment-application', compact('recruitment'))->render();
        }
        return $content;
    }


    public function renderRecruitmentPage(?string $content, Page $page): ?string
    {
        if ($page->getKey() == $this->getRecruitmentPageId()) {
            $view = 'plugins/recruitment::themes.recruitments';

            if (view()->exists($viewPath = Theme::getThemeNamespace() . '::views.themes.recruitments')) {
                $view = $viewPath;
            }

            $currentLanguage = Language::getCurrentLocaleCode();
            if ($currentLanguage == 'en') {
                $currentLanguage = 'en_US';
            }

            // Get data for search form with language support
            $categories = RecruitmentCategory::query()
                ->join('language_meta', function ($join) use ($currentLanguage) {
                    $join->on('language_meta.reference_id', '=', 'recruitment_categories.id')
                        ->where('language_meta.reference_type', RecruitmentCategory::class)
                        ->where('language_meta.lang_meta_code', $currentLanguage);
                })
                ->where('recruitment_categories.status', BaseStatusEnum::PUBLISHED)
                ->select('recruitment_categories.id', 'recruitment_categories.name', 'recruitment_categories.order')
                ->orderBy('recruitment_categories.order')
                ->orderBy('recruitment_categories.name')
                ->get();

            $provinces = Province::query()
                ->join('language_meta', function ($join) use ($currentLanguage) {
                    $join->on('language_meta.reference_id', '=', 'recruitment_provinces.id')
                        ->where('language_meta.reference_type', Province::class)
                        ->where('language_meta.lang_meta_code', $currentLanguage);
                })
                ->where('recruitment_provinces.status', BaseStatusEnum::PUBLISHED)
                ->select('recruitment_provinces.id', 'recruitment_provinces.name', 'recruitment_provinces.order')
                ->orderBy('recruitment_provinces.order')
                ->orderBy('recruitment_provinces.name')
                ->get();

            $workTypes = WorkType::query()
                ->join('language_meta', function ($join) use ($currentLanguage) {
                    $join->on('language_meta.reference_id', '=', 'recruitment_work_types.id')
                        ->where('language_meta.reference_type', WorkType::class)
                        ->where('language_meta.lang_meta_code', $currentLanguage);
                })
                ->where('recruitment_work_types.status', BaseStatusEnum::PUBLISHED)
                ->select('recruitment_work_types.id', 'recruitment_work_types.name', 'recruitment_work_types.order')
                ->orderBy('recruitment_work_types.order')
                ->orderBy('recruitment_work_types.name')
                ->get();

            // Fetch recruitment listings with search functionality
            $query = Recruitment::query()
                ->join('language_meta', function ($join) use ($currentLanguage) {
                    $join->on('language_meta.reference_id', '=', 'recruitments.id')
                        ->where('language_meta.reference_type', Recruitment::class)
                        ->where('language_meta.lang_meta_code', $currentLanguage);
                })
                ->where('recruitments.status', BaseStatusEnum::PUBLISHED);

            $request = request();

            // Search by keyword
            $query->when($request->input('keyword'), function ($query, $keyword) {
                return $query->where(function ($q) use ($keyword) {
                    $q->where('recruitments.name', 'like', "%{$keyword}%")
                      ->orWhere('recruitments.description', 'like', "%{$keyword}%");
                });
            });

            // Filter by category
            $query->when($request->input('category_id'), function ($query, $categoryId) {
                return $query->whereHas('categories', function ($q) use ($categoryId) {
                    $q->where('recruitment_categories.id', $categoryId);
                });
            });

            // Filter by province
            $query->when($request->input('province_id'), function ($query, $provinceId) {
                return $query->where('recruitments.province_id', $provinceId);
            });

            // Filter by district
            $query->when($request->input('district_id'), function ($query, $districtId) {
                return $query->where('recruitments.district_id', $districtId);
            });

            // Filter by work type
            $query->when($request->input('work_type_id'), function ($query, $workTypeId) {
                return $query->whereHas('workTypes', function ($q) use ($workTypeId) {
                    $q->where('recruitment_work_types.id', $workTypeId);
                });
            });

            $recruitments = $query->with(['categories', 'workTypes', 'tags', 'province', 'district'])
                ->select('recruitments.*')
                ->orderBy('recruitments.is_featured', 'desc')
                ->orderBy('recruitments.created_at', 'desc')
                ->paginate(12)
                ->appends(request()->query());

            // Add recruitments to the view data
            $viewData = compact('page', 'categories', 'provinces', 'workTypes', 'recruitments');

            // Use the current page's URL for the breadcrumb
            Theme::breadcrumb()->add($page->name, $page->url);

            return view($view, $viewData)->render();
        }

        return $content;
    }

    protected function getRecruitmentPageId()
    {
        return theme_option('recruitment_page_id', setting('recruitment_page_id'));
    }


    protected function getRecruitmentApplicationPageId()
    {
        return theme_option('recruitment_application_page_id', setting('recruitment_application_page_id'));
    }
}

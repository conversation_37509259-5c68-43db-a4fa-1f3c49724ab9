<?php

namespace Bo<PERSON>ble\Recruitment\Providers;

use <PERSON><PERSON>ble\Base\Enums\BaseStatusEnum;
use <PERSON><PERSON>ble\Base\Supports\ServiceProvider;
use <PERSON><PERSON>ble\Recruitment\Models\Recruitment;
use Bo<PERSON>ble\Shortcode\Compilers\Shortcode;
use Bo<PERSON>ble\Theme\Facades\Theme;
use Illuminate\Support\Str;
use Botble\Language\Facades\Language;


class ShortcodeProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (!function_exists('shortcode')) {
            return;
        }

        shortcode()->register('recruitment-list', __('Recruitment List'), __('Recruitment List'), function (Shortcode $shortcode) {
            $limit = (int)$shortcode->limit ?: 4;
            $title = $shortcode->title ?: __('C<PERSON> hội nghề nghiệp ở CHAGEE');
            $view_more_url = $shortcode->view_more_url ?: route('public.recruitments');
            $view_more_text = $shortcode->view_more_text ?: __('Tìm hiểu thêm');
            $language = $shortcode->language ?: app()->getLocale();



            $recruitments = Recruitment::query()
                ->wherePublished()
                ->join('language_meta', function ($join) use ($language) {
                    $join->on('language_meta.reference_id', '=', 'recruitments.id')
                        ->where('language_meta.reference_type', Recruitment::class)
                        ->where('language_meta.lang_meta_code', $language);
                })
                ->with(['province', 'district'])
                ->orderBy('recruitments.created_at', 'desc')
                ->limit($limit)
                ->get();

            $view = 'plugins/recruitment::partials.shortcodes.recruitment-list';
            $themeView = Theme::getThemeNamespace() . '::views.partials.shortcodes.recruitment-list';

            if (view()->exists($themeView)) {
                $view = $themeView;
            }

            return view($view, compact('recruitments', 'title', 'view_more_url', 'view_more_text'));
        });
    }
}

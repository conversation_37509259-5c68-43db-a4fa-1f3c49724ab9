<?php

namespace Bo<PERSON>ble\Recruitment\Forms;

use Bo<PERSON>ble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Bo<PERSON>ble\Recruitment\Models\RecruitmentApplication;

class RecruitmentApplicationForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(RecruitmentApplication::class)
            ->addMetaBoxes([
                'information' => [
                    'title' => trans('plugins/recruitment::recruitment.application_information'),
                    'content' => view('plugins/recruitment::application-info', ['application' => $this->getModel()])->render(),
                ],
            ])
            ->add('status', SelectField::class, StatusFieldOption::make()->toArray())
            ->setBreakFieldPoint('status');
    }
}

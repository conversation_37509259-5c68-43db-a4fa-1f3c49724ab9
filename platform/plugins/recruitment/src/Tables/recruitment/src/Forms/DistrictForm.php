<?php

namespace Bo<PERSON>ble\Recruitment\Forms;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Botble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\OnOffField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Recruitment\Http\Requests\DistrictRequest;
use Botble\Recruitment\Models\District;
use Botble\Recruitment\Models\Province;

class DistrictForm extends FormAbstract
{
    public function buildForm(): void
    {
        $provinces = Province::query()
            ->wherePublished()
            ->pluck('name', 'id')
            ->all();

        $this
            ->setupModel(new District())
            ->setValidatorClass(DistrictRequest::class)
            ->withCustomFields()
            ->add('name', TextField::class, NameFieldOption::make()
                ->required()
                ->toArray()
            )
            ->add('province_id', SelectField::class, [
                'label' => trans('plugins/recruitment::recruitment.province'),
                'label_attr' => ['class' => 'control-label required'],
                'choices' => $provinces,
            ])
            ->add('status', SelectField::class, StatusFieldOption::make()->toArray())
            ->add('order', NumberField::class, [
                'label' => trans('core/base::forms.order'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('core/base::forms.order_by_placeholder'),
                ],
                'default_value' => 0,
            ])
            ->add('is_default', OnOffField::class, [
                'label' => trans('core/base::forms.is_default'),
                'label_attr' => ['class' => 'control-label'],
                'default_value' => false,
            ])
            ->setBreakFieldPoint('status');
    }
}

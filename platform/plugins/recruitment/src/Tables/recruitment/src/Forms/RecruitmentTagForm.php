<?php

namespace Bo<PERSON>ble\Recruitment\Forms;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Base\Forms\FieldOptions\DescriptionFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\NameFieldOption;
use Botble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Recruitment\Http\Requests\RecruitmentTagRequest;
use Botble\Recruitment\Models\RecruitmentTag;

class RecruitmentTagForm extends FormAbstract
{
    public function buildForm(): void
    {
        $this
            ->setupModel(new RecruitmentTag())
            ->setValidatorClass(RecruitmentTagRequest::class)
            ->withCustomFields()
            ->add('name', TextField::class, NameFieldOption::make()
                ->required()
                ->toArray()
            )
            ->add('color', TextField::class, [
                'label' => trans('plugins/recruitment::recruitment.tag_color'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => '#FB8600',
                    'class' => 'form-control',
                    'type' => 'color',
                ],
                'default_value' => '#FB8600',
            ])
            ->add('description', TextareaField::class, DescriptionFieldOption::make()->toArray())
            ->add('status', SelectField::class, StatusFieldOption::make()->toArray())
            ->add('order', NumberField::class, [
                'label' => trans('core/base::forms.order'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('core/base::forms.order_by_placeholder'),
                ],
                'default_value' => 0,
            ])
            ->setBreakFieldPoint('status');
    }
}

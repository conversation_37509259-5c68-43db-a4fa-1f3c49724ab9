<?php

namespace Botble\Recruitment\Forms\Fronts;

use Botble\Base\Forms\FieldOptions\EmailFieldOption;
use Botble\Base\Forms\FieldOptions\HtmlFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\EmailField;
use Botble\Base\Forms\Fields\HtmlField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\DateField;
use Botble\Base\Forms\Fields\RadioField;
use Botble\Base\Models\BaseModel;
use Botble\Language\Facades\Language;
use Botble\Recruitment\Http\Requests\RecruitmentApplicationRequest;
use Botble\Recruitment\Models\Province;
use Botble\Recruitment\Models\District;
use Bo<PERSON>ble\Recruitment\Models\Recruitment;
use Botble\Theme\FormFront;
use Botble\Captcha\Facades\Captcha;
use Botble\Captcha\Forms\Fields\ReCaptchaField;


class RecruitmentApplicationForm extends FormFront
{
    protected static ?BaseModel $reference = null;

    public function setup(): void
    {
        $currentLanguage = Language::getCurrentLocaleCode();
        if ($currentLanguage == 'en') {
            $currentLanguage = 'en_US';
        }

        // Lấy recruitment từ parameter URL
        $recruitmentId = request()->input('recruitment_id');
        $recruitment = null;
        $recruitmentName = __('Vui lòng chọn vị trí ứng tuyển'); // Default value

        if ($recruitmentId) {
            $recruitment = Recruitment::query()
                ->join('language_meta', function ($join) use ($currentLanguage) {
                    $join->on('language_meta.reference_id', '=', 'recruitments.id')
                        ->where('language_meta.reference_type', Recruitment::class)
                        ->where('language_meta.lang_meta_code', $currentLanguage);
                })
                ->where('recruitments.id', $recruitmentId)
                ->where('recruitments.status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
                ->select('recruitments.*')
                ->first();

            if ($recruitment) {
                $recruitmentName = $recruitment->name;
                static::$reference = $recruitment;
            }
        }

        $provinces = Province::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_provinces.id')
                    ->where('language_meta.reference_type', Province::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->where('recruitment_provinces.status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
            ->orderBy('recruitment_provinces.order')
            ->orderBy('recruitment_provinces.name')
            ->pluck('recruitment_provinces.name', 'recruitment_provinces.id')
            ->all();

        $this
            ->contentOnly()
            ->setFormOption('class', 'wrap-form-recruitment form-style-general')
            ->setUrl(route('public.recruitment.application.store'))
            ->setValidatorClass(RecruitmentApplicationRequest::class)
            ->add('lang_code', 'hidden', ['value' => Language::getCurrentLocale()]);

        // Thêm recruitment_id và recruitment_name từ parameter hoặc reference
        if ($recruitment) {
            $this
                ->add('recruitment_id', 'hidden', ['value' => $recruitment->id])
                ->add('recruitment_name', 'hidden', ['value' => $recruitment->name]);
        } elseif ($recruitmentId) {
            // Nếu có recruitment_id nhưng không tìm thấy recruitment
            $this
                ->add('recruitment_id', 'hidden', ['value' => $recruitmentId])
                ->add('recruitment_name', 'hidden', ['value' => $recruitmentName]);
        }

        // Generate province options
        $provinceOptions = '';
        foreach ($provinces as $id => $name) {
            $provinceOptions .= '<option value="' . $id . '">' . htmlspecialchars($name) . '</option>';
        }

        $this
            // Title
            ->add('form_application_html', HtmlField::class, HtmlFieldOption::make()
                ->content('
							<div class="title title-40 mb-base text-Primary-S1">Form thông tin ứng tuyển</div>
							<div class="wrap-form flex flex-col py-10 lg:px-16 px-5 shadow-Dropshadow-Light rounded-5">
								<table class="basic-information">
									<tr>
										<td>
											<label for="name">Vị trí tuyển dụng</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" name="position_display" value="' . htmlspecialchars($recruitmentName) . '" disabled>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Họ và tên *</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="Họ và tên" name="full_name" required="required"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Ngày sinh *</label>
										</td>
										<td>
											<div class="form-group input-date-picker">
												<input id="calendar" type="text" placeholder="Chọn ngày" name="birthday" required>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Giới tính *</label>
										</td>
										<td>
											<div class="form-group">
												<select name="gender" placeholder="Giới tính" required="required">
													<option value="">Chọn giới tính</option>
													<option value="male">Nam</option>
													<option value="female">Nữ</option>
													<option value="other">Khác</option>
												</select>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Số điện thoại *</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="Số điện thoại" name="phone" required="required"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Email *</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="Email" name="email" required="required"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Địa điểm</label>
										</td>
										<td>
											<table class="w-full wrap-table-3-columns">
												<tr>
													<td>
														<label for="name">Địa điểm làm việc mong muốn 1 *:</label>
													</td>
													<td>
														<div class="form-group">
															<select name="location_province_1" placeholder="Tỉnh/Thành phố" class="province-select" data-target="location_district_1" required="required">
																<option value=""></option>
																' . $provinceOptions . '
															</select>
														</div>
													</td>
													<td>
														<div class="form-group">
															<select name="location_district_1" placeholder="Quận/Huyện" class="district-select" required="required">
																<option value=""></option>
															</select>
														</div>
													</td>
												</tr>
											</table>
											<table class="w-full wrap-table-3-columns">
												<tr>
													<td>
														<label for="name">Địa điểm làm việc mong muốn 2:</label>
													</td>
													<td>
														<div class="form-group">
															<select name="location_province_2" placeholder="Tỉnh/Thành phố" class="province-select" data-target="location_district_2">
																<option value=""></option>
																' . $provinceOptions . '
															</select>
														</div>
													</td>
													<td>
														<div class="form-group">
															<select name="location_district_2" placeholder="Quận/Huyện" class="district-select">
																<option value=""></option>
															</select>
														</div>
													</td>
												</tr>
											</table>
											<table class="w-full wrap-table-3-columns">
												<tr>
													<td>
														<label for="name">Địa điểm làm việc mong muốn 3:</label>
													</td>
													<td>
														<div class="form-group">
															<select name="location_province_3" placeholder="Tỉnh/Thành phố" class="province-select" data-target="location_district_3">
																<option value=""></option>
																' . $provinceOptions . '
															</select>
														</div>
													</td>
													<td>
														<div class="form-group">
															<select name="location_district_3" placeholder="Quận/Huyện" class="district-select">
																<option value=""></option>
															</select>
														</div>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Ứng cử viên được tuyển lại</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="Nhập mã nhân viên cũ" name="old_employee_code"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">Nhập mã nhân viên giới thiệu</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="Nhập mã nhân viên giới thiệu" name="referral_code"/>
											</div>
										</td>
									</tr>
								</table>
								<table class="table-checkbox">
									<tr>
										<td>
											<label>Đăng ký nhận bản tin tuyển dụng</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="newsletter_yes" name="newsletter" value="yes" required>
													<label for="newsletter_yes">Có</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="newsletter_no" name="newsletter" value="no">
													<label for="newsletter_no">Không</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>Bạn có thể làm ca xoay được không?</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="shift_work_yes" name="can_work_shifts" value="yes" required>
													<label for="shift_work_yes">Có</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="shift_work_no" name="can_work_shifts" value="no">
													<label for="shift_work_no">Không</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>Bạn có thể làm cuối tuần được không?</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="weekend_work_yes" name="can_work_weekends" value="yes" required>
													<label for="weekend_work_yes">Có</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="weekend_work_no" name="can_work_weekends" value="no">
													<label for="weekend_work_no">Không</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>Bạn có thể làm trong các ngày Lễ / Tết không?</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="holiday_work_yes" name="can_work_holidays" value="yes" required>
													<label for="holiday_work_yes">Có</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="holiday_work_no" name="can_work_holidays" value="no">
													<label for="holiday_work_no">Không</label>
												</div>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label>Bạn làm được bao nhiêu ngày/tuần ?</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="work_days_under_4" name="work_days" value="under_4" required>
													<label for="work_days_under_4">Dưới 4 ngày</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="work_days_4_to_6" name="work_days" value="4_to_6">
													<label for="work_days_4_to_6">4 - 6 ngày</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="work_days_7" name="work_days" value="7_days">
													<label for="work_days_7">7 ngày</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>Thời gian bạn mong muốn gắn bó với CHAGEE</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="commitment_under_3" name="commitment_duration" value="under_3_months" required>
													<label for="commitment_under_3">Dưới 3 tháng</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="commitment_3_to_6" name="commitment_duration" value="3_to_6_months">
													<label for="commitment_3_to_6">3 - 6 tháng</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="commitment_over_6" name="commitment_duration" value="over_6_months">
													<label for="commitment_over_6">Trên 6 tháng</label>
												</div>
											</div>
										</td>
									</tr>
								</table>
								<table class="basic-information mt-5">
									<tr>
										<td>
											<label for="name">File CV*</label>
										</td>
										<td>
											<div class="form-group form-input-file">
												<input type="file" accept=".pdf,.doc,.docx,.png,.jpg,.jpeg" placeholder="Tải lên CV của bạn" name="cv_file" required>
											</div>
											<div class="note text-Neutral-800 mt-3">File đính kèm (pdf, doc, docx, png, jpg, jpeg)</div>
										</td>
									</tr>
								</table>
								<div class="frm-submit mt-5 flex items-center justify-between">
									<div class="wrap-captcha"></div>
									<div class="wrap-btn-submit">
										<button class="btn btn-btn-primary secondary w-full flex-center" type="submit"> <span>Ứng tuyển ngay</span>
											<div class="btn-icon btn-lined"><span class="icon to-right"><i class="fa-regular fa-arrow-right"></i><i class="fa-regular fa-arrow-right"></i></span></div>
										</button>
									</div>
								</div>
							</div>
						')
                ->toArray());
    }

    public static function createWithReference(BaseModel $model): self
    {
        static::$reference = $model;
        return app(static::class);
    }

    public static function getReference(): ?BaseModel
    {
        return static::$reference;
    }
}

<?php

namespace Botble\Recruitment\Forms;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Botble\Base\Forms\FormAbstract;
use Botble\Base\Forms\FieldOptions\ContentFieldOption;
use Botble\Base\Forms\FieldOptions\DescriptionFieldOption;
use Botble\Base\Forms\FieldOptions\IsFeaturedFieldOption;
use Botble\Base\Forms\FieldOptions\MediaImageFieldOption;
use Botble\Base\Forms\FieldOptions\NameFieldOption;
use Botble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\Fields\DatePickerField;
use Botble\Base\Forms\Fields\EditorField;
use Botble\Base\Forms\Fields\MediaImageField;
use Botble\Base\Forms\Fields\MultiCheckListField;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\OnOffField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Bo<PERSON>ble\Recruitment\Http\Requests\RecruitmentRequest;
use Botble\Recruitment\Models\District;
use Botble\Recruitment\Models\Province;
use Botble\Recruitment\Models\Recruitment;
use Botble\Recruitment\Models\RecruitmentCategory;
use Botble\Recruitment\Models\RecruitmentTag;
use Botble\Recruitment\Models\WorkType;
use Botble\Language\Models\Language;

class RecruitmentForm extends FormAbstract
{
    public function buildForm(): void
    {
        if ($this->getModel()) {
            $post_id = $this->getModel()->getKey();
            $currentLanguage = Language::query()
                ->join('language_meta', function ($join) use ($post_id) {
                    $join->on('language_meta.lang_meta_code', '=', 'languages.lang_code')
                        ->where('language_meta.reference_id', $post_id)
                        ->where('language_meta.reference_type', Recruitment::class);
                })
                ->value('lang_code');
        } else {
            $currentLanguage = request()->query('ref_lang', 'vi');
        }

        $provinces = Province::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_provinces.id')
                    ->where('language_meta.reference_type', Province::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->wherePublished()
            ->select('recruitment_provinces.id', 'recruitment_provinces.name')
            ->pluck('name', 'id')
            ->all();

        $districts = [];
        if ($this->getModel() && $this->getModel()->province_id) {
            $districts = District::query()
                ->join('language_meta', function ($join) use ($currentLanguage) {
                    $join->on('language_meta.reference_id', '=', 'recruitment_districts.id')
                        ->where('language_meta.reference_type', District::class)
                        ->where('language_meta.lang_meta_code', $currentLanguage);
                })
                ->wherePublished()
                ->where('recruitment_districts.province_id', $this->getModel()->province_id)
                ->select('recruitment_districts.id', 'recruitment_districts.name')
                ->pluck('name', 'id')
                ->all();
        }

        $categories = RecruitmentCategory::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_categories.id')
                    ->where('language_meta.reference_type', RecruitmentCategory::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->wherePublished()
            ->select('recruitment_categories.id', 'recruitment_categories.name')
            ->pluck('name', 'id')
            ->all();

        $workTypes = WorkType::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_work_types.id')
                    ->where('language_meta.reference_type', WorkType::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->wherePublished()
            ->select('recruitment_work_types.id', 'recruitment_work_types.name')
            ->pluck('name', 'id')
            ->all();

        $tags = RecruitmentTag::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_tags.id')
                    ->where('language_meta.reference_type', RecruitmentTag::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->wherePublished()
            ->select('recruitment_tags.id', 'recruitment_tags.name')
            ->pluck('name', 'id')
            ->all();

        $this
            ->setupModel(new Recruitment())
            ->setValidatorClass(RecruitmentRequest::class)
            ->withCustomFields()
            ->add('name', TextField::class, NameFieldOption::make()
                ->label(trans('core/base::forms.name'))
                ->required()
                ->toArray()
            )
            ->add('description', TextareaField::class, DescriptionFieldOption::make()->toArray())
            ->add('quantity', NumberField::class, [
                'label' => trans('plugins/recruitment::recruitment.quantity'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.quantity_placeholder'),
                    'min' => 1,
                ],
                'default_value' => 1,
            ])
            ->add('salary', TextField::class, [
                'label' => trans('plugins/recruitment::recruitment.salary'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.salary_placeholder'),
                    'data-counter' => 255,
                ],
            ])
            ->add('work_mode', TextField::class, [
                'label' => trans('plugins/recruitment::recruitment.work_mode'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.work_mode_placeholder'),
                    'data-counter' => 255,
                ],
            ])
            ->add('product_benefits', TextareaField::class, [
                'label' => trans('plugins/recruitment::recruitment.product_benefits'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.product_benefits_placeholder'),
                    'rows' => 3,
                ],
            ])
            ->add('business_revenue', TextField::class, [
                'label' => trans('plugins/recruitment::recruitment.business_revenue'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.business_revenue_placeholder'),
                    'data-counter' => 255,
                ],
            ])
            ->add('working_hours', TextField::class, [
                'label' => trans('plugins/recruitment::recruitment.working_hours'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.working_hours_placeholder'),
                    'data-counter' => 255,
                ],
            ])
            ->add('promotion_opportunities', TextareaField::class, [
                'label' => trans('plugins/recruitment::recruitment.promotion_opportunities'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.promotion_opportunities_placeholder'),
                    'rows' => 3,
                ],
            ])
            ->add('internal_bonding_programs', TextareaField::class, [
                'label' => trans('plugins/recruitment::recruitment.internal_bonding_programs'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.internal_bonding_programs_placeholder'),
                    'rows' => 3,
                ],
            ])
            ->add('job_level', TextField::class, [
                'label' => trans('plugins/recruitment::recruitment.job_level'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.job_level_placeholder'),
                    'data-counter' => 255,
                ],
            ])
            ->add('health_checkups', TextareaField::class, [
                'label' => trans('plugins/recruitment::recruitment.health_checkups'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.health_checkups_placeholder'),
                    'rows' => 3,
                ],
            ])
            ->add('deadline', DatePickerField::class, [
                'label' => trans('plugins/recruitment::recruitment.deadline'),
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'placeholder' => trans('plugins/recruitment::recruitment.deadline_placeholder'),
                ],
            ])
            ->add('is_featured', OnOffField::class, IsFeaturedFieldOption::make()->toArray())
            ->add('content', EditorField::class, ContentFieldOption::make()
                ->label(trans('plugins/recruitment::recruitment.job_description'))
                ->allowedShortcodes()
                ->toArray()
            )
            ->add('requirements', EditorField::class, ContentFieldOption::make()
                ->label(trans('plugins/recruitment::recruitment.job_requirements'))
                ->allowedShortcodes()
                ->toArray()
            )
            ->add('benefits', EditorField::class, ContentFieldOption::make()
                ->label(trans('plugins/recruitment::recruitment.job_benefits'))
                ->allowedShortcodes()
                ->toArray()
            )
            ->add('status', SelectField::class, StatusFieldOption::make()->toArray())
            ->add('categories[]', MultiCheckListField::class, [
                'label' => trans('plugins/recruitment::recruitment.categories'),
                'label_attr' => ['class' => 'control-label'],
                'choices' => $categories,
                'value' => $this->getModel()->id ? $this->getModel()->categories()->pluck('category_id')->all() : [],
            ])
            ->add('work_types[]', MultiCheckListField::class, [
                'label' => trans('plugins/recruitment::recruitment.work_types'),
                'label_attr' => ['class' => 'control-label'],
                'choices' => $workTypes,
                'value' => $this->getModel()->id ? $this->getModel()->workTypes()->pluck('work_type_id')->all() : [],
            ])
            ->add('tags[]', MultiCheckListField::class, [
                'label' => trans('plugins/recruitment::recruitment.tags'),
                'label_attr' => ['class' => 'control-label'],
                'choices' => $tags,
                'value' => $this->getModel()->id ? $this->getModel()->tags()->pluck('tag_id')->all() : [],
            ])
            ->add('province_id', SelectField::class, [
                'label' => trans('plugins/recruitment::recruitment.province'),
                'label_attr' => ['class' => 'control-label'],
                'choices' => $provinces,
                'attr' => [
                    'class' => 'form-control select-search-full',
                    'data-type' => 'province',
                    'data-placeholder' => trans('plugins/recruitment::recruitment.select_province'),
                ],
            ])
            ->add('district_id', SelectField::class, [
                'label' => trans('plugins/recruitment::recruitment.district'),
                'label_attr' => ['class' => 'control-label'],
                'choices' => $districts,
                'attr' => [
                    'class' => 'form-control select-search-full',
                    'data-type' => 'district',
                    'data-placeholder' => trans('plugins/recruitment::recruitment.select_district'),
                ],
            ])
            ->add('image', MediaImageField::class, MediaImageFieldOption::make()->toArray())
            ->setBreakFieldPoint('status');
    }
}

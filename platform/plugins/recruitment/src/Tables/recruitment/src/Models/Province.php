<?php

namespace Bo<PERSON>ble\Recruitment\Models;

use Bo<PERSON>ble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Botble\Base\Models\Concerns\HasSlug;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Province extends BaseModel
{
    use HasSlug;

    protected $table = 'recruitment_provinces';

    protected $fillable = [
        'name',
        'slug',
        'order',
        'is_default',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'is_default' => 'boolean',
        'order' => 'integer',
    ];

    protected static function booted(): void
    {
        static::deleted(function (Province $province) {
            $province->districts()->delete();
        });

        static::saving(function (self $model) {
            $model->slug = self::createSlug($model->slug ?: $model->name, $model->getKey());
        });
    }

    public function districts(): HasMany
    {
        return $this->hasMany(District::class, 'province_id');
    }

    public function recruitments(): HasMany
    {
        return $this->hasMany(Recruitment::class, 'province_id');
    }
}

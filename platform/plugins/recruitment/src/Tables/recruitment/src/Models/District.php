<?php

namespace Bo<PERSON>ble\Recruitment\Models;

use Botble\Base\Casts\SafeContent;
use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Botble\Base\Models\Concerns\HasSlug;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class District extends BaseModel
{
    use HasSlug;

    protected $table = 'recruitment_districts';

    protected $fillable = [
        'name',
        'province_id',
        'slug',
        'order',
        'is_default',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'is_default' => 'boolean',
        'order' => 'integer',
    ];

    protected static function booted(): void
    {
        self::saving(function (self $model) {
            $model->slug = self::createSlug($model->slug ?: $model->name, $model->getKey());
        });
    }

    public function province(): BelongsTo
    {
        return $this->belongsTo(Province::class, 'province_id')->withDefault();
    }

    public function recruitments(): HasMany
    {
        return $this->hasMany(Recruitment::class, 'district_id');
    }
}

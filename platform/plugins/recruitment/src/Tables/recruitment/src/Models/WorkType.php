<?php

namespace Bo<PERSON>ble\Recruitment\Models;

use Botble\Base\Casts\SafeContent;
use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WorkType extends BaseModel
{
    protected $table = 'recruitment_work_types';

    protected $fillable = [
        'name',
        'description',
        'status',
        'order',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
    ];

    public function recruitments(): HasMany
    {
        return $this->hasMany(Recruitment::class, 'work_type_id');
    }
}

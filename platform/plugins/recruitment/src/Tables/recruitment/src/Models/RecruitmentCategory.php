<?php

namespace Bo<PERSON>ble\Recruitment\Models;

use Botble\Base\Casts\SafeContent;
use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RecruitmentCategory extends BaseModel
{
    protected $table = 'recruitment_categories';

    protected $fillable = [
        'name',
        'description',
        'status',
        'order',
        'is_default',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
        'is_default' => 'boolean',
    ];

    public function recruitments(): HasMany
    {
        return $this->hasMany(Recruitment::class, 'category_id');
    }
}

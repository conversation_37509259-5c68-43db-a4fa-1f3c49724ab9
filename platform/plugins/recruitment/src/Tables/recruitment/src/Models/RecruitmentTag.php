<?php

namespace Bo<PERSON>ble\Recruitment\Models;

use Botble\Base\Casts\SafeContent;
use Botble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class RecruitmentTag extends BaseModel
{
    protected $table = 'recruitment_tags';

    protected $fillable = [
        'name',
        'slug',
        'color',
        'description',
        'status',
        'order',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
        'name' => SafeContent::class,
        'description' => SafeContent::class,
    ];

    public function recruitments(): BelongsToMany
    {
        return $this->belongsToMany(
            Recruitment::class,
            'recruitment_tag_pivot',
            'tag_id',
            'recruitment_id'
        )->withTimestamps();
    }

    protected static function booted(): void
    {
        static::creating(function (RecruitmentTag $tag) {
            if (empty($tag->slug)) {
                $tag->slug = \Str::slug($tag->name);
            }
        });

        static::updating(function (RecruitmentTag $tag) {
            if ($tag->isDirty('name') && empty($tag->slug)) {
                $tag->slug = \Str::slug($tag->name);
            }
        });
    }
}

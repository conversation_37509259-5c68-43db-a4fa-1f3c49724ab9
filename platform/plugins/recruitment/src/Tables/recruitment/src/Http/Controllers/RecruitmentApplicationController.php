<?php

namespace Bo<PERSON><PERSON>\Recruitment\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\RecruitmentApplicationForm;
use Bo<PERSON>ble\Recruitment\Exports\RecruitmentApplicationExport;
use Botble\Recruitment\Models\RecruitmentApplication;
use Botble\Recruitment\Tables\RecruitmentApplicationTable;
use Maatwebsite\Excel\Facades\Excel;
use Exception;
use Illuminate\Http\Request;

class RecruitmentApplicationController extends BaseController
{
    public function __construct()
    {
        $this
            ->breadcrumb()
            ->add(trans('plugins/recruitment::recruitment.applications'), route('recruitment-applications.index'));
    }

    public function index(RecruitmentApplicationTable $table)
    {
        $this->pageTitle(trans('plugins/recruitment::recruitment.applications'));

        return $table->renderTable();
    }

    public function create()
    {
        $this->pageTitle(trans('plugins/recruitment::recruitment.create_application'));

        return RecruitmentApplicationForm::create()->renderForm();
    }

    public function edit(RecruitmentApplication $application, Request $request)
    {
        event(new BeforeEditContentEvent($request, $application));

        $this->pageTitle(trans('plugins/recruitment::recruitment.edit_application', ['name' => $application->full_name]));

        return RecruitmentApplicationForm::createFromModel($application)->renderForm();
    }

    public function store(Request $request, BaseHttpResponse $response)
    {
        $application = RecruitmentApplication::create($request->input());

        event(new CreatedContentEvent(RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME, $request, $application));

        return $response
            ->setPreviousUrl(route('recruitment-applications.index'))
            ->setNextUrl(route('recruitment-applications.edit', $application->getKey()))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function show(RecruitmentApplication $application, Request $request)
    {
        event(new BeforeEditContentEvent($request, $application));

        $this->pageTitle(trans('plugins/recruitment::recruitment.view_application', ['name' => $application->full_name]));

        return RecruitmentApplicationForm::createFromModel($application)->renderForm();
    }

    public function update(RecruitmentApplication $application, Request $request, BaseHttpResponse $response)
    {
        $application->fill($request->input());
        $application->save();

        event(new UpdatedContentEvent(RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME, $request, $application));

        return $response
            ->setPreviousUrl(route('recruitment-applications.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(RecruitmentApplication $application, Request $request, BaseHttpResponse $response)
    {
        try {
            $application->delete();

            event(new DeletedContentEvent(RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME, $request, $application));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function downloadCv(RecruitmentApplication $application)
    {
        if (!$application->cv_file_path || !file_exists(storage_path('app/public/' . $application->cv_file_path))) {
            abort(404, 'File CV không tồn tại');
        }

        return response()->download(
            storage_path('app/public/' . $application->cv_file_path),
            $application->cv_file_name ?: 'cv.pdf'
        );
    }

    public function export(Request $request, RecruitmentApplicationTable $table)
    {
        // Get filtered query from table
        $query = $table->query();
        $filteredQuery = $table->applyScopes($query);

        // Debug: Check if export class is working
        $export = new RecruitmentApplicationExport($filteredQuery);
        $collection = $export->collection();

        // Log for debugging
        \Log::info('Export collection count: ' . $collection->count());
        \Log::info('Export headings count: ' . count($export->headings()));
        \Log::info('Export query SQL: ' . $filteredQuery->toSql());
        \Log::info('Export query bindings: ' . json_encode($filteredQuery->getBindings()));

        return Excel::download($export, 'recruitment_applications_' . date('Y-m-d_H-i-s') . '.xlsx');
    }
}

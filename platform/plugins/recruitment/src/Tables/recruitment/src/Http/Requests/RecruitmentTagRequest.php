<?php

namespace Bo<PERSON>ble\Recruitment\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class RecruitmentTagRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:120',
            'color' => 'required|string|size:7',
            'description' => 'nullable|string|max:400',
            'status' => Rule::in(BaseStatusEnum::values()),
            'order' => 'nullable|integer|min:0|max:127',
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => trans('core/base::forms.name'),
            'color' => trans('plugins/recruitment::recruitment.tag_color'),
            'description' => trans('core/base::forms.description'),
            'status' => trans('core/base::forms.status'),
            'order' => trans('core/base::forms.order'),
        ];
    }
}

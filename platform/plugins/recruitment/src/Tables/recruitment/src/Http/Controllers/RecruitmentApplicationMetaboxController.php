<?php

namespace Bo<PERSON>ble\Recruitment\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Recruitment\Models\Recruitment;
use Bo<PERSON>ble\Recruitment\Models\RecruitmentApplication;
use Illuminate\Http\Request;

class RecruitmentApplicationMetaboxController extends BaseController
{
    public function index(Request $request, $recruitmentId)
    {
        $recruitment = Recruitment::findOrFail($recruitmentId);
        
        $applications = RecruitmentApplication::query()
            ->where('recruitment_id', $recruitmentId)
            ->with(['locationProvince1', 'locationDistrict1'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('plugins/recruitment::metaboxes.applications', compact('recruitment', 'applications'));
    }
}

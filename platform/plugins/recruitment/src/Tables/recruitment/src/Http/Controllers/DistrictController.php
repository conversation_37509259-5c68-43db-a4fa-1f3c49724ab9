<?php

namespace Bo<PERSON><PERSON>\Recruitment\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\DistrictForm;
use Bo<PERSON>ble\Recruitment\Http\Requests\DistrictRequest;
use Bo<PERSON>ble\Recruitment\Models\District;
use Bo<PERSON>ble\Recruitment\Tables\DistrictTable;
use Exception;
use Illuminate\Http\Request;

class DistrictController extends BaseController
{
    public function index(DistrictTable $table)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.districts'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.create_district'));

        return $formBuilder->create(DistrictForm::class)->renderForm();
    }

    public function store(DistrictRequest $request, BaseHttpResponse $response)
    {
        $district = District::query()->create($request->input());

        event(new CreatedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $district));

        return $response
            ->setPreviousUrl(route('recruitment-districts.index'))
            ->setNextUrl(route('recruitment-districts.edit', $district->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(District $district, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $district));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $district->name]));

        return $formBuilder->create(DistrictForm::class, ['model' => $district])->renderForm();
    }

    public function update(District $district, DistrictRequest $request, BaseHttpResponse $response)
    {
        $district->fill($request->input());
        $district->save();

        event(new UpdatedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $district));

        return $response
            ->setPreviousUrl(route('recruitment-districts.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(District $district, Request $request, BaseHttpResponse $response)
    {
        try {
            $district->delete();

            event(new DeletedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $district));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }

    public function getDistrictsByProvince($provinceId, BaseHttpResponse $response)
    {
        if (!$provinceId) {
            return $response->setError()->setMessage('Province ID is required');
        }

        $districts = District::query()
            ->where('province_id', $provinceId)
            ->wherePublished()
            ->orderBy('order')
            ->orderBy('name')
            ->select(['id', 'name'])
            ->get();

        return $response
            ->setError(false)
            ->setMessage('Success')
            ->setData($districts);
    }
}

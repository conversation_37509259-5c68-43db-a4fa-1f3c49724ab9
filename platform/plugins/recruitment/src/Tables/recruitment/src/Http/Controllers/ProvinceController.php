<?php

namespace Bo<PERSON><PERSON>\Recruitment\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\ProvinceForm;
use Botble\Recruitment\Http\Requests\ProvinceRequest;
use Bo<PERSON>ble\Recruitment\Models\Province;
use Botble\Recruitment\Tables\ProvinceTable;
use Exception;
use Illuminate\Http\Request;

class ProvinceController extends BaseController
{
    public function index(ProvinceTable $table)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.provinces'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.create_province'));

        return $formBuilder->create(ProvinceForm::class)->renderForm();
    }

    public function store(ProvinceRequest $request, BaseHttpResponse $response)
    {
        $province = Province::query()->create($request->input());

        event(new CreatedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $province));

        return $response
            ->setPreviousUrl(route('recruitment-provinces.index'))
            ->setNextUrl(route('recruitment-provinces.edit', $province->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(Province $province, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $province));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $province->name]));

        return $formBuilder->create(ProvinceForm::class, ['model' => $province])->renderForm();
    }

    public function update(Province $province, ProvinceRequest $request, BaseHttpResponse $response)
    {
        $province->fill($request->input());
        $province->save();

        event(new UpdatedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $province));

        return $response
            ->setPreviousUrl(route('recruitment-provinces.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(Province $province, Request $request, BaseHttpResponse $response)
    {
        try {
            $province->delete();

            event(new DeletedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $province));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

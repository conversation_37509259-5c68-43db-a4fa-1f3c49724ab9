<?php

namespace Bo<PERSON>ble\Recruitment\Http\Controllers;

use Botble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\RecruitmentForm;
use Botble\Recruitment\Http\Requests\RecruitmentRequest;
use Bo<PERSON>ble\Recruitment\Models\Recruitment;
use Bo<PERSON>ble\Recruitment\Tables\RecruitmentTable;
use Exception;
use Illuminate\Http\Request;

class RecruitmentController extends BaseController
{
    public function index(RecruitmentTable $table)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.create'));

        return $formBuilder->create(RecruitmentForm::class)->renderForm();
    }

    public function store(RecruitmentRequest $request, BaseHttpResponse $response)
    {
        $recruitment = Recruitment::query()->create($request->except(['categories', 'work_types']));

        // Sync categories
        if ($request->input('categories')) {
            $recruitment->categories()->sync($request->input('categories', []));
        }

        // Sync work types
        if ($request->input('work_types')) {
            $recruitment->workTypes()->sync($request->input('work_types', []));
        }

        // Sync tags
        if ($request->input('tags')) {
            $recruitment->tags()->sync($request->input('tags', []));
        }

        event(new CreatedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $recruitment));

        return $response
            ->setPreviousUrl(route('recruitment.index'))
            ->setNextUrl(route('recruitment.edit', $recruitment->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(Recruitment $recruitment, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $recruitment));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $recruitment->name]));

        return $formBuilder->create(RecruitmentForm::class, ['model' => $recruitment])->renderForm();
    }

    public function update(Recruitment $recruitment, RecruitmentRequest $request, BaseHttpResponse $response)
    {
        $recruitment->fill($request->except(['categories', 'work_types', 'tags']));
        $recruitment->save();

        // Sync categories
        if ($request->has('categories')) {
            $recruitment->categories()->sync($request->input('categories', []));
        }

        // Sync work types
        if ($request->has('work_types')) {
            $recruitment->workTypes()->sync($request->input('work_types', []));
        }

        // Sync tags
        if ($request->has('tags')) {
            $recruitment->tags()->sync($request->input('tags', []));
        }

        event(new UpdatedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $recruitment));

        return $response
            ->setPreviousUrl(route('recruitment.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(Recruitment $recruitment, Request $request, BaseHttpResponse $response)
    {
        try {
            $recruitment->delete();

            event(new DeletedContentEvent(RECRUITMENT_MODULE_SCREEN_NAME, $request, $recruitment));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

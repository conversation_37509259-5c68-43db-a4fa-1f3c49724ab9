<?php

namespace Bo<PERSON><PERSON>\Recruitment\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\RecruitmentTagForm;
use Botble\Recruitment\Http\Requests\RecruitmentTagRequest;
use Botble\Recruitment\Models\RecruitmentTag;
use Bo<PERSON>ble\Recruitment\Tables\RecruitmentTagTable;
use Exception;
use Illuminate\Http\Request;

class RecruitmentTagController extends BaseController
{
    public function index(RecruitmentTagTable $table)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.tags'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.create_tag'));

        return $formBuilder->create(RecruitmentTagForm::class)->renderForm();
    }

    public function store(RecruitmentTagRequest $request, BaseHttpResponse $response)
    {
        $tag = RecruitmentTag::query()->create($request->input());

        event(new CreatedContentEvent(RECRUITMENT_TAG_MODULE_SCREEN_NAME, $request, $tag));

        return $response
            ->setPreviousUrl(route('recruitment-tags.index'))
            ->setNextUrl(route('recruitment-tags.edit', $tag->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(RecruitmentTag $tag, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $tag));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $tag->name]));

        return $formBuilder->create(RecruitmentTagForm::class, ['model' => $tag])->renderForm();
    }

    public function update(RecruitmentTag $tag, RecruitmentTagRequest $request, BaseHttpResponse $response)
    {
        $tag->fill($request->input());
        $tag->save();

        event(new UpdatedContentEvent(RECRUITMENT_TAG_MODULE_SCREEN_NAME, $request, $tag));

        return $response
            ->setPreviousUrl(route('recruitment-tags.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(RecruitmentTag $tag, Request $request, BaseHttpResponse $response)
    {
        try {
            $tag->delete();

            event(new DeletedContentEvent(RECRUITMENT_TAG_MODULE_SCREEN_NAME, $request, $tag));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

<?php

namespace Bo<PERSON><PERSON>\Recruitment\Http\Controllers;

use Bo<PERSON>ble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\WorkTypeForm;
use Bo<PERSON>ble\Recruitment\Http\Requests\WorkTypeRequest;
use Bo<PERSON>ble\Recruitment\Models\WorkType;
use Bo<PERSON>ble\Recruitment\Tables\WorkTypeTable;
use Exception;
use Illuminate\Http\Request;

class WorkTypeController extends BaseController
{
    public function index(WorkTypeTable $table)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.work_types'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.create_work_type'));

        return $formBuilder->create(WorkTypeForm::class)->renderForm();
    }

    public function store(WorkTypeRequest $request, BaseHttpResponse $response)
    {
        $workType = WorkType::query()->create($request->input());

        event(new CreatedContentEvent(RECRUITMENT_WORK_TYPE_MODULE_SCREEN_NAME, $request, $workType));

        return $response
            ->setPreviousUrl(route('recruitment-work-types.index'))
            ->setNextUrl(route('recruitment-work-types.edit', $workType->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(WorkType $workType, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $workType));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $workType->name]));

        return $formBuilder->create(WorkTypeForm::class, ['model' => $workType])->renderForm();
    }

    public function update(WorkType $workType, WorkTypeRequest $request, BaseHttpResponse $response)
    {
        $workType->fill($request->input());
        $workType->save();

        event(new UpdatedContentEvent(RECRUITMENT_WORK_TYPE_MODULE_SCREEN_NAME, $request, $workType));

        return $response
            ->setPreviousUrl(route('recruitment-work-types.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(WorkType $workType, Request $request, BaseHttpResponse $response)
    {
        try {
            $workType->delete();

            event(new DeletedContentEvent(RECRUITMENT_WORK_TYPE_MODULE_SCREEN_NAME, $request, $workType));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

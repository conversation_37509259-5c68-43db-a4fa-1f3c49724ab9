<?php

namespace Bo<PERSON><PERSON>\Recruitment\Http\Controllers;

use Botble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Recruitment\Forms\RecruitmentCategoryForm;
use Botble\Recruitment\Http\Requests\RecruitmentCategoryRequest;
use Botble\Recruitment\Models\RecruitmentCategory;
use Botble\Recruitment\Tables\RecruitmentCategoryTable;
use Exception;
use Illuminate\Http\Request;

class RecruitmentCategoryController extends BaseController
{
    public function index(RecruitmentCategoryTable $table)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.categories'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/recruitment::recruitment.create_category'));

        return $formBuilder->create(RecruitmentCategoryForm::class)->renderForm();
    }

    public function store(RecruitmentCategoryRequest $request, BaseHttpResponse $response)
    {
        $category = RecruitmentCategory::query()->create($request->input());

        event(new CreatedContentEvent(RECRUITMENT_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

        return $response
            ->setPreviousUrl(route('recruitment-categories.index'))
            ->setNextUrl(route('recruitment-categories.edit', $category->id))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function edit(RecruitmentCategory $category, FormBuilder $formBuilder, Request $request)
    {
        event(new BeforeEditContentEvent($request, $category));

        PageTitle::setTitle(trans('core/base::forms.edit_item', ['name' => $category->name]));

        return $formBuilder->create(RecruitmentCategoryForm::class, ['model' => $category])->renderForm();
    }

    public function update(RecruitmentCategory $category, RecruitmentCategoryRequest $request, BaseHttpResponse $response)
    {
        $category->fill($request->input());
        $category->save();

        event(new UpdatedContentEvent(RECRUITMENT_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

        return $response
            ->setPreviousUrl(route('recruitment-categories.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(RecruitmentCategory $category, Request $request, BaseHttpResponse $response)
    {
        try {
            $category->delete();

            event(new DeletedContentEvent(RECRUITMENT_CATEGORY_MODULE_SCREEN_NAME, $request, $category));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}

<?php

namespace Bo<PERSON>ble\Recruitment\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class DistrictRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'province_id' => 'required|exists:recruitment_provinces,id',
            'status' => Rule::in(BaseStatusEnum::values()),
            'order' => 'nullable|integer|min:0',
            'is_default' => 'nullable|boolean',
        ];
    }
}

<?php

namespace Bo<PERSON>ble\Recruitment\Http\Requests;

use Botble\Support\Http\Requests\Request;

class RecruitmentApplicationRequest extends Request
{
    public function rules(): array
    {
        $rules = [
            'recruitment_id' => 'nullable|integer|exists:recruitments,id',
            'recruitment_name' => 'nullable|string|max:255',
            'lang_code' => 'nullable|string|max:10',
            'full_name' => 'required|string|max:255',
            'birthday' => 'required|date_format:d/m/Y|before:today',
            'gender' => 'required|in:male,female,other',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'location_province_1' => 'required|integer|exists:recruitment_provinces,id',
            'location_district_1' => 'required|integer|exists:recruitment_districts,id',
            'location_province_2' => 'nullable|integer|exists:recruitment_provinces,id',
            'location_district_2' => 'nullable|integer|exists:recruitment_districts,id',
            'location_province_3' => 'nullable|integer|exists:recruitment_provinces,id',
            'location_district_3' => 'nullable|integer|exists:recruitment_districts,id',
            'old_employee_code' => 'nullable|string|max:50',
            'referral_code' => 'nullable|string|max:50',
            'newsletter' => 'required|in:yes,no',
            'can_work_shifts' => 'required|in:yes,no',
            'can_work_weekends' => 'required|in:yes,no',
            'can_work_holidays' => 'required|in:yes,no',
            'work_days' => 'required|in:under_4,4_to_6,7_days',
            'commitment_duration' => 'required|in:under_3_months,3_to_6_months,over_6_months',
            'cv_file' => 'required|file|mimes:pdf,doc,docx,png,jpg,jpeg|max:10240', // 10MB max
        ];

        // if (is_plugin_active('captcha')) {
        //     $rules['g-recaptcha-response'] = ['required', 'captcha'];
        // }

        return $rules;
    }

    public function attributes(): array
    {
        return [
            'full_name' => 'Họ và tên',
            'birthday' => 'Ngày sinh',
            'gender' => 'Giới tính',
            'phone' => 'Số điện thoại',
            'email' => 'Email',
            'location_province_1' => 'Tỉnh/Thành phố mong muốn 1',
            'location_district_1' => 'Quận/Huyện mong muốn 1',
            'location_province_2' => 'Tỉnh/Thành phố mong muốn 2',
            'location_district_2' => 'Quận/Huyện mong muốn 2',
            'location_province_3' => 'Tỉnh/Thành phố mong muốn 3',
            'location_district_3' => 'Quận/Huyện mong muốn 3',
            'old_employee_code' => 'Mã nhân viên cũ',
            'referral_code' => 'Mã nhân viên giới thiệu',
            'newsletter' => 'Đăng ký nhận bản tin',
            'can_work_shifts' => 'Có thể làm ca xoay',
            'can_work_weekends' => 'Có thể làm cuối tuần',
            'can_work_holidays' => 'Có thể làm ngày lễ/tết',
            'work_days' => 'Số ngày làm việc/tuần',
            'commitment_duration' => 'Thời gian gắn bó mong muốn',
            'cv_file' => 'File CV',
        ];
    }

    public function messages(): array
    {
        return [
            'birthday.date_format' => 'Ngày sinh không đúng định dạng. Vui lòng chọn ngày từ lịch.',
            'birthday.before' => 'Ngày sinh phải trước ngày hôm nay.',
            'cv_file.required' => 'Vui lòng tải lên file CV.',
            'cv_file.mimes' => 'File CV phải có định dạng: pdf, doc, docx, zip, rar.',
            'cv_file.max' => 'File CV không được vượt quá 10MB.',
        ];
    }
}

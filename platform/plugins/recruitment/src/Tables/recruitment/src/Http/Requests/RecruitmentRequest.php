<?php

namespace Bo<PERSON>ble\Recruitment\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class RecruitmentRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:400',
            'content' => 'nullable|string',
            'requirements' => 'nullable|string',
            'benefits' => 'nullable|string',
            'status' => Rule::in(BaseStatusEnum::values()),
            'category_id' => 'nullable|exists:recruitment_categories,id',
            'work_type_id' => 'nullable|exists:recruitment_work_types,id',
            'state_id' => 'nullable|exists:states,id',
            'city_id' => 'nullable|exists:cities,id',
            'quantity' => 'nullable|integer|min:1',
            'salary' => 'nullable|string|max:255',
            'work_mode' => 'nullable|string|max:255',
            'product_benefits' => 'nullable|string',
            'business_revenue' => 'nullable|string|max:255',
            'working_hours' => 'nullable|string|max:255',
            'promotion_opportunities' => 'nullable|string',
            'internal_bonding_programs' => 'nullable|string',
            'job_level' => 'nullable|string|max:255',
            'health_checkups' => 'nullable|string',
            'deadline' => 'nullable|date',
            'image' => 'nullable|string',
            'is_featured' => 'nullable|boolean',
        ];
    }
}

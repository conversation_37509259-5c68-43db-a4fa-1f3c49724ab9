<?php

namespace Bo<PERSON><PERSON>\Recruitment\Services;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Base\Supports\Helper;
use <PERSON><PERSON>ble\Recruitment\Models\Recruitment;
use <PERSON>tble\Recruitment\Models\RecruitmentCategory;
use Bo<PERSON>ble\Recruitment\Models\Province;
use Bo<PERSON>ble\Recruitment\Models\District;
use Bo<PERSON>ble\Recruitment\Models\WorkType;
use Botble\Media\Facades\RvMedia;
use Bo<PERSON>ble\SeoHelper\Facades\SeoHelper;
use Botble\SeoHelper\SeoOpenGraph;
use Botble\Slug\Models\Slug;
use Botble\Theme\Facades\AdminBar;
use Botble\Theme\Facades\Theme;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Botble\Language\Facades\Language;

class RecruitmentService
{
    public function handleFrontRoutesRecruitment($slug)
    {
        if (!$slug instanceof Slug) {
            return $slug;
        }

        $condition = [
            'recruitments.id' => $slug->reference_id,
            'recruitments.status' => BaseStatusEnum::PUBLISHED,
        ];

        if (Auth::guard()->check() && request()->input('preview')) {
            Arr::forget($condition, 'recruitments.status');
        }

        switch ($slug->reference_type) {
            case Recruitment::class:
                $currentLanguage = Language::getCurrentLocaleCode();
                if ($currentLanguage == 'en') {
                    $currentLanguage = 'en_US';
                }

                $recruitment = Recruitment::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitments.id')
                            ->where('language_meta.reference_type', Recruitment::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where($condition)
                    ->with(['categories', 'workTypes', 'tags', 'province', 'district'])
                    ->select('recruitments.*')
                    ->first();

                if (empty($recruitment)) {
                    abort(404);
                }

                Helper::handleViewCount($recruitment, 'viewed_recruitment');

                SeoHelper::setTitle($recruitment->name)
                    ->setDescription($recruitment->description);

                $meta = new SeoOpenGraph();
                if ($recruitment->image) {
                    $meta->setImage(RvMedia::getImageUrl($recruitment->image));
                }
                $meta->setDescription($recruitment->description);
                $meta->setUrl($recruitment->url);
                $meta->setTitle($recruitment->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);
                SeoHelper::meta()->setUrl($recruitment->url);

                if (function_exists('admin_bar')) {
                    AdminBar::registerLink(
                        trans('plugins/recruitment::recruitment.edit_this_recruitment'),
                        route('recruitment.edit', $recruitment->id),
                        null,
                        'recruitment.edit'
                    );
                }

                // Get data for search form with language support
                $categories = RecruitmentCategory::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitment_categories.id')
                            ->where('language_meta.reference_type', RecruitmentCategory::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where('recruitment_categories.status', BaseStatusEnum::PUBLISHED)
                    ->select('recruitment_categories.id', 'recruitment_categories.name', 'recruitment_categories.order')
                    ->orderBy('recruitment_categories.order')
                    ->orderBy('recruitment_categories.name')
                    ->get();

                $provinces = Province::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitment_provinces.id')
                            ->where('language_meta.reference_type', Province::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where('recruitment_provinces.status', BaseStatusEnum::PUBLISHED)
                    ->select('recruitment_provinces.id', 'recruitment_provinces.name', 'recruitment_provinces.order')
                    ->orderBy('recruitment_provinces.order')
                    ->orderBy('recruitment_provinces.name')
                    ->get();

                $workTypes = WorkType::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitment_work_types.id')
                            ->where('language_meta.reference_type', WorkType::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where('recruitment_work_types.status', BaseStatusEnum::PUBLISHED)
                    ->select('recruitment_work_types.id', 'recruitment_work_types.name', 'recruitment_work_types.order')
                    ->orderBy('recruitment_work_types.order')
                    ->orderBy('recruitment_work_types.name')
                    ->get();

                // Get related recruitments (latest posts, same category or province) with language support
                $relatedRecruitments = Recruitment::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitments.id')
                            ->where('language_meta.reference_type', Recruitment::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where('recruitments.status', BaseStatusEnum::PUBLISHED)
                    ->where('recruitments.id', '!=', $recruitment->id)
                    ->where(function ($query) use ($recruitment) {
                        // Same category or same province
                        if ($recruitment->categories->isNotEmpty()) {
                            $categoryIds = $recruitment->categories->pluck('id')->toArray();
                            $query->whereHas('categories', function ($q) use ($categoryIds) {
                                $q->whereIn('recruitment_categories.id', $categoryIds);
                            });
                        }
                        if ($recruitment->province_id) {
                            $query->orWhere('recruitments.province_id', $recruitment->province_id);
                        }
                    })
                    ->with(['categories', 'workTypes', 'tags', 'province', 'district'])
                    ->select('recruitments.*')
                    ->orderBy('recruitments.created_at', 'desc')
                    ->limit(3)
                    ->get();

                // If not enough related posts, get latest posts
                if ($relatedRecruitments->count() < 3) {
                    $excludeIds = $relatedRecruitments->pluck('id')->push($recruitment->id)->toArray();
                    $latestRecruitments = Recruitment::query()
                        ->join('language_meta', function ($join) use ($currentLanguage) {
                            $join->on('language_meta.reference_id', '=', 'recruitments.id')
                                ->where('language_meta.reference_type', Recruitment::class)
                                ->where('language_meta.lang_meta_code', $currentLanguage);
                        })
                        ->where('recruitments.status', BaseStatusEnum::PUBLISHED)
                        ->whereNotIn('recruitments.id', $excludeIds)
                        ->with(['categories', 'workTypes', 'tags', 'province', 'district'])
                        ->select('recruitments.*')
                        ->orderBy('recruitments.created_at', 'desc')
                        ->limit(3 - $relatedRecruitments->count())
                        ->get();

                    $relatedRecruitments = $relatedRecruitments->merge($latestRecruitments);
                }

                Theme::breadcrumb()
                    ->add(__('Home'), route('public.index'))
                    // ->add(__('Recruitment'), route('public.recruitment'))
                    ->add($recruitment->name, $recruitment->url);

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, RECRUITMENT_MODULE_SCREEN_NAME, $recruitment);

                $recruitment_page_id = theme_option('recruitment_page_id', setting('recruitment_page_id'));
                $recruitment_page = \Botble\Page\Models\Page::find($recruitment_page_id);
                $recruitment_page_url = $recruitment_page->url;

                return [
                    'view' => 'plugins/recruitment::themes.recruitment',
                    'default_view' => 'plugins/recruitment::themes.recruitment',
                    'data' => compact('recruitment', 'categories', 'provinces', 'workTypes', 'relatedRecruitments', 'recruitment_page_url'),
                    'slug' => $slug->key,
                ];

            case RecruitmentCategory::class:
                $currentLanguage = Language::getCurrentLocaleCode();
                if ($currentLanguage == 'en') {
                    $currentLanguage = 'en_US';
                }

                $categoryCondition = [
                    'recruitment_categories.id' => $slug->reference_id,
                    'recruitment_categories.status' => BaseStatusEnum::PUBLISHED,
                ];

                if (Auth::guard()->check() && request()->input('preview')) {
                    Arr::forget($categoryCondition, 'recruitment_categories.status');
                }

                $category = RecruitmentCategory::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitment_categories.id')
                            ->where('language_meta.reference_type', RecruitmentCategory::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where($categoryCondition)
                    ->select('recruitment_categories.*')
                    ->first();

                if (empty($category)) {
                    abort(404);
                }

                SeoHelper::setTitle($category->name)
                    ->setDescription($category->description);

                $meta = new SeoOpenGraph();
                $meta->setDescription($category->description);
                $meta->setUrl($category->url);
                $meta->setTitle($category->name);
                $meta->setType('article');

                SeoHelper::setSeoOpenGraph($meta);
                SeoHelper::meta()->setUrl($category->url);

                if (function_exists('admin_bar')) {
                    AdminBar::registerLink(
                        trans('plugins/recruitment::recruitment.edit_this_category'),
                        route('recruitment-categories.edit', $category->id),
                        null,
                        'recruitment-categories.edit'
                    );
                }

                $recruitments = Recruitment::query()
                    ->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitments.id')
                            ->where('language_meta.reference_type', Recruitment::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where('recruitments.status', BaseStatusEnum::PUBLISHED)
                    ->whereHas('categories', function ($query) use ($category) {
                        $query->where('recruitment_categories.id', $category->id);
                    })
                    ->with(['categories', 'workTypes', 'province', 'district'])
                    ->select('recruitments.*')
                    ->orderBy('recruitments.created_at', 'desc')
                    ->paginate(12);

                Theme::breadcrumb()
                    ->add(__('Home'), route('public.index'))
                    // ->add(__('Recruitment'), route('public.recruitment'))
                    ->add($category->name, $category->url);

                do_action(BASE_ACTION_PUBLIC_RENDER_SINGLE, RECRUITMENT_CATEGORY_MODULE_SCREEN_NAME, $category);

                return [
                    'view' => 'plugins/recruitment::themes.category',
                    'default_view' => 'plugins/recruitment::themes.category',
                    'data' => compact('category', 'recruitments'),
                    'slug' => $slug->key,
                ];
        }

        return $slug;
    }

    public function renderRecruitmentPage(?string $content, \Botble\Page\Models\Page $page): ?string
    {
        if ($page->template !== 'recruitment') {
            return $content;
        }

        $currentLanguage = Language::getCurrentLocaleCode();
        if ($currentLanguage == 'en') {
            $currentLanguage = 'en_US';
        }

        $categories = RecruitmentCategory::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_categories.id')
                    ->where('language_meta.reference_type', RecruitmentCategory::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->where('recruitment_categories.status', BaseStatusEnum::PUBLISHED)
            ->with([
                'recruitments' => function ($query) use ($currentLanguage) {
                    $query->join('language_meta', function ($join) use ($currentLanguage) {
                        $join->on('language_meta.reference_id', '=', 'recruitments.id')
                            ->where('language_meta.reference_type', Recruitment::class)
                            ->where('language_meta.lang_meta_code', $currentLanguage);
                    })
                    ->where('recruitments.status', BaseStatusEnum::PUBLISHED)
                    ->orderBy('recruitments.created_at', 'desc')
                    ->with(['categories', 'workTypes', 'province', 'district'])
                    ->select('recruitments.*');
                },
            ])
            ->select('recruitment_categories.*')
            ->orderBy('recruitment_categories.created_at', 'desc')
            ->get();

        return Theme::view('plugins/recruitment::themes.templates.recruitments', compact('categories', 'page'))->render();
    }
}

<?php

use <PERSON><PERSON>ble\Base\Enums\BaseStatusEnum;
use Botble\Recruitment\Models\District;
use Bo<PERSON>ble\Recruitment\Models\Province;
use Bo<PERSON>ble\Recruitment\Models\Recruitment;
use Bo<PERSON>ble\Recruitment\Models\RecruitmentCategory;
use Bo<PERSON>ble\Recruitment\Models\WorkType;

if (!function_exists('get_featured_recruitments')) {
    /**
     * @param int $limit
     * @param array $with
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function get_featured_recruitments(int $limit = 5, array $with = []): \Illuminate\Database\Eloquent\Collection
    {
        return Recruitment::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->where('is_featured', true)
            ->with(array_merge(['categories', 'workTypes', 'province', 'district'], $with))
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}

if (!function_exists('get_latest_recruitments')) {
    /**
     * @param int $limit
     * @param array $with
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function get_latest_recruitments(int $limit = 5, array $with = []): \Illuminate\Database\Eloquent\Collection
    {
        return Recruitment::query()
            ->where('status', BaseStatusEnum::PUBLISHED)
            ->with(array_merge(['categories', 'workTypes', 'province', 'district'], $with))
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}

if (!function_exists('get_recruitment_categories')) {
    /**
     * @param array $args
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function get_recruitment_categories(array $args = []): \Illuminate\Database\Eloquent\Collection
    {
        $args = array_merge([
            'status' => BaseStatusEnum::PUBLISHED,
            'order_by' => 'created_at',
            'order' => 'desc',
            'take' => null,
            'with' => [],
        ], $args);

        $query = RecruitmentCategory::query()
            ->where('status', $args['status']);

        if (!empty($args['with'])) {
            $query = $query->with($args['with']);
        }

        if ($args['order_by'] && $args['order']) {
            $query = $query->orderBy($args['order_by'], $args['order']);
        }

        if ($args['take']) {
            $query = $query->take($args['take']);
        }

        return $query->get();
    }
}

if (!function_exists('get_work_types')) {
    /**
     * @param array $args
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function get_work_types(array $args = []): \Illuminate\Database\Eloquent\Collection
    {
        $args = array_merge([
            'status' => BaseStatusEnum::PUBLISHED,
            'order_by' => 'created_at',
            'order' => 'desc',
            'take' => null,
        ], $args);

        $query = WorkType::query()
            ->where('status', $args['status']);

        if ($args['order_by'] && $args['order']) {
            $query = $query->orderBy($args['order_by'], $args['order']);
        }

        if ($args['take']) {
            $query = $query->take($args['take']);
        }

        return $query->get();
    }
}

if (!function_exists('get_provinces')) {
    /**
     * @param array $args
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function get_provinces(array $args = []): \Illuminate\Database\Eloquent\Collection
    {
        $args = array_merge([
            'status' => BaseStatusEnum::PUBLISHED,
            'order_by' => 'order',
            'order' => 'asc',
            'take' => null,
        ], $args);

        $query = Province::query()
            ->where('status', $args['status']);

        if ($args['order_by'] && $args['order']) {
            $query = $query->orderBy($args['order_by'], $args['order']);
        }

        if ($args['take']) {
            $query = $query->take($args['take']);
        }

        return $query->get();
    }
}

if (!function_exists('get_districts_by_province')) {
    /**
     * @param int|string|null $provinceId
     * @param array $args
     * @return \Illuminate\Database\Eloquent\Collection
     */
    function get_districts_by_province($provinceId, array $args = []): \Illuminate\Database\Eloquent\Collection
    {
        if (!$provinceId) {
            return collect([]);
        }

        $args = array_merge([
            'status' => BaseStatusEnum::PUBLISHED,
            'order_by' => 'order',
            'order' => 'asc',
            'take' => null,
        ], $args);

        $query = District::query()
            ->where('province_id', $provinceId)
            ->where('status', $args['status']);

        if ($args['order_by'] && $args['order']) {
            $query = $query->orderBy($args['order_by'], $args['order']);
        }

        if ($args['take']) {
            $query = $query->take($args['take']);
        }

        return $query->get();
    }
}

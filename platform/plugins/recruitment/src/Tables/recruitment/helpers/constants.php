<?php

if (! defined('RECRUITMENT_MODULE_SCREEN_NAME')) {
    define('RECRUITMENT_MODULE_SCREEN_NAME', 'recruitment');
}

if (! defined('RECRUITMENT_CATEGORY_MODULE_SCREEN_NAME')) {
    define('RECRUITMENT_CATEGORY_MODULE_SCREEN_NAME', 'recruitment-category');
}

if (! defined('RECRUITMENT_TAG_MODULE_SCREEN_NAME')) {
    define('RECRUITMENT_TAG_MODULE_SCREEN_NAME', 'recruitment-tag');
}

if (! defined('RECRUITMENT_WORK_TYPE_MODULE_SCREEN_NAME')) {
    define('RECRUITMENT_WORK_TYPE_MODULE_SCREEN_NAME', 'recruitment-work-type');
}

if (! defined('RECRUITMENT_PROVINCE_MODULE_SCREEN_NAME')) {
    define('RECRUITMENT_PROVINCE_MODULE_SCREEN_NAME', 'recruitment-province');
}

if (! defined('RECRUITMENT_DISTRICT_MODULE_SCREEN_NAME')) {
    define('RECRUITMENT_DISTRICT_MODULE_SCREEN_NAME', 'recruitment-district');
}

if (! defined('RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME')) {
    define('RECRUITMENT_APPLICATION_MODULE_SCREEN_NAME', 'recruitment-application');
}

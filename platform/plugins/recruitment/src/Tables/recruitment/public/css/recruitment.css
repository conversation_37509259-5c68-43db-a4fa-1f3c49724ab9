.recruitment-item {
    border: 1px solid #eee;
    border-radius: 5px;
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
}

.recruitment-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.recruitment-item .image {
    height: 200px;
    overflow: hidden;
}

.recruitment-item .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.recruitment-item:hover .image img {
    transform: scale(1.05);
}

.recruitment-item .info {
    padding: 15px;
}

.recruitment-item .title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.recruitment-item .title a {
    color: #333;
    text-decoration: none;
}

.recruitment-item .title a:hover {
    color: #007bff;
}

.recruitment-item .desc {
    color: #666;
    margin-bottom: 10px;
    font-size: 14px;
}

.recruitment-item .meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 13px;
    color: #777;
}

.recruitment-item .meta span {
    background: #f5f5f5;
    padding: 3px 8px;
    border-radius: 3px;
}

.recruitment-detail-section {
    padding: 50px 0;
}

.box-recruitment-info {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.box-recruitment-info .left {
    flex: 1;
}

.box-recruitment-info .right {
    width: 300px;
}

.box-recruitment-info .right img {
    width: 100%;
    border-radius: 5px;
}

.box-recruitment-detail {
    background: #f9f9f9;
    padding: 30px;
    border-radius: 5px;
}

.box-recruitment-detail h2 {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.box-recruitment-detail .content {
    color: #555;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .box-recruitment-info {
        flex-direction: column;
    }
    
    .box-recruitment-info .right {
        width: 100%;
    }
}

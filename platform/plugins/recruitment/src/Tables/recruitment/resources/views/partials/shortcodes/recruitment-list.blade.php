<section class="home-6"> 
    <div class="container"> 
        <h2 class="title-40 mb-6 text-center mb-base">{{ $title }}</h2>
        <div class="home-6-list grid lg:grid-cols-4 grid-cols-2 lg:gap-6 gap-3 mb-base">
            @foreach($recruitments as $recruitment)

            <div class="item group">
                <div class="image"> 
                    <a class="img-ratio zoom-img rounded-lg" href="{{ $recruitment->url }}">
                        <img class="lozad undefined" data-src="{{ RvMedia::getImageUrl($recruitment->image, 'medium', false, RvMedia::getDefaultImage()) }}" alt="{{ $recruitment->name }}"/>
                    </a>
                </div>
                <div class="content py-3">
                    <h3 class="text-xl font-bold text-Primary-S1 leading-[1.3] group-hover:text-Primary-3 mb-2">
                        <a href="{{ $recruitment->url }}">{{ $recruitment->name }}</a>
                    </h3>
                    <div class="wrap flex flex-col gap-1 md:gap-0 md:flex-row items-start md:items-center justify-between text-Neutral-800 font-medium">
                        <div class="address flex items-center gap-3">
                            <i class="fa-light fa-location-dot"></i>
                            <a href="{{ $recruitment->url }}">
                                <span>
                                    @if($recruitment->province)

                                        {{ $recruitment->district->name ? $recruitment->district->name . ', ' : '' }}{{ $recruitment->province->name }}
                                    @endif
                                </span>
                            </a>
                        </div>
                        <div class="quantity flex items-center gap-3">
                            <i class="fa-light fa-user"></i>
                            <a href="{{ $recruitment->url }}">
                                <span>{{ __('Số lượng:') }} {{ $recruitment->quantity }}</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        <div class="home-6-btn flex-center">
            <a class="btn-btn-primary inline-flex items-center group" href="{{ $view_more_url }}">
                <span>{{ $view_more_text }}</span>
                <div class="btn-icon btn-lined">
                    <span class="icon to-right"> 
                        <i class="fa-regular fa-arrow-right"></i>
                        <i class="fa-regular fa-arrow-right"></i>
                    </span>
                </div>
            </a>
        </div>
    </div>
</section>

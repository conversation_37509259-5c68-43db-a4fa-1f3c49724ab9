@if ($recruitments->count() > 0)
    <div class="row">
        @foreach ($recruitments as $recruitment)
            <div class="col-md-4 mb-4">
                <div class="recruitment-item">
                    <div class="image">
                        <a href="{{ $recruitment->url }}">
                            <img src="{{ RvMedia::getImageUrl($recruitment->image, 'thumb', false, RvMedia::getDefaultImage()) }}" alt="{{ $recruitment->name }}">
                        </a>
                    </div>
                    <div class="info">
                        <h3 class="title">
                            <a href="{{ $recruitment->url }}">{{ $recruitment->name }}</a>
                        </h3>
                        <div class="desc">
                            <p>{{ Str::limit($recruitment->description, 100) }}</p>
                        </div>
                        <div class="meta">
                            @if ($recruitment->workTypes->count() > 0)
                                <span class="work-type">{{ $recruitment->workTypes->pluck('name')->first() }}</span>
                            @endif
                            @if ($recruitment->province)
                                <span class="location">{{ $recruitment->province->name }}</span>
                            @endif
                            <span class="salary">{{ $recruitment->salary }}</span>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    @if (!request()->ajax())
        <div class="pagination">
            {!! $recruitments->links() !!}
        </div>
    @endif
@else
    <div class="alert alert-info">
        {{ __('No recruitments found') }}
    </div>
@endif

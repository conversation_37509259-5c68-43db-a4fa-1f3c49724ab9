@php
    use Bo<PERSON><PERSON>\Recruitment\Forms\Fronts\RecruitmentApplicationForm;
    use Botble\Recruitment\Models\Province;
    use Botble\Recruitment\Models\District;
@endphp

<section class="section-page-recruitment-form section-py">
    <div class="container">
        @if(isset($recruitment))
            {!! RecruitmentApplicationForm::createWithReference($recruitment)->renderForm() !!}
        @else
            {!! RecruitmentApplicationForm::create()->renderForm() !!}
        @endif
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle province change to load districts for location 1
    const provinceSelect1 = document.querySelector('select[name="location_province_1"]');
    const districtSelect1 = document.querySelector('select[name="location_district_1"]');
    
    if (provinceSelect1 && districtSelect1) {
        provinceSelect1.addEventListener('change', function() {
            const provinceId = this.value;
            loadDistricts(provinceId, districtSelect1);
        });
    }

    // Handle province change to load districts for location 2
    const provinceSelect2 = document.querySelector('select[name="location_province_2"]');
    const districtSelect2 = document.querySelector('select[name="location_district_2"]');
    
    if (provinceSelect2 && districtSelect2) {
        provinceSelect2.addEventListener('change', function() {
            const provinceId = this.value;
            loadDistricts(provinceId, districtSelect2);
        });
    }

    // Handle province change to load districts for location 3
    const provinceSelect3 = document.querySelector('select[name="location_province_3"]');
    const districtSelect3 = document.querySelector('select[name="location_district_3"]');
    
    if (provinceSelect3 && districtSelect3) {
        provinceSelect3.addEventListener('change', function() {
            const provinceId = this.value;
            loadDistricts(provinceId, districtSelect3);
        });
    }

    function loadDistricts(provinceId, districtSelect) {
        // Clear district options
        districtSelect.innerHTML = '<option value="">Chọn quận/huyện</option>';
        
        if (provinceId) {
            // Load districts via AJAX
            fetch(`{{ route('public.recruitment.get-districts') }}?province_id=${provinceId}`)
                .then(response => response.json())
                .then(data => {
                    data.forEach(district => {
                        const option = document.createElement('option');
                        option.value = district.id;
                        option.textContent = district.name;
                        districtSelect.appendChild(option);
                    });
                })
                .catch(error => console.error('Error loading districts:', error));
        }
    }

    // Handle form submission
    const form = document.querySelector('.wrap-form-recruitment');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            
            // Disable submit button
            submitButton.disabled = true;
            submitButton.innerHTML = '<span>Đang gửi...</span>';
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.message || 'Có lỗi xảy ra khi gửi đơn ứng tuyển.');
                } else {
                    alert(data.message || 'Đơn ứng tuyển đã được gửi thành công!');
                    this.reset();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi gửi đơn ứng tuyển. Vui lòng thử lại sau.');
            })
            .finally(() => {
                // Re-enable submit button
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            });
        });
    }
});
</script>

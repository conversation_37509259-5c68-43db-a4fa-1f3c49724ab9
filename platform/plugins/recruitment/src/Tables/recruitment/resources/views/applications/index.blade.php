@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fa fa-filter"></i> {{ __('Bộ lọc') }}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('recruitment-applications.index') }}" class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="province_id">{{ __('Tỉnh/Thành phố') }}</label>
                                <select name="province_id" id="province_id" class="form-control">
                                    <option value="">{{ __('Tất cả tỉnh/thành phố') }}</option>
                                    @foreach($provinces as $province)
                                        <option value="{{ $province->id }}" {{ request('province_id') == $province->id ? 'selected' : '' }}>
                                            {{ $province->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="district_id">{{ __('Quận/Huyện') }}</label>
                                <select name="district_id" id="district_id" class="form-control">
                                    <option value="">{{ __('Tất cả quận/huyện') }}</option>
                                    @foreach($districts as $district)
                                        <option value="{{ $district->id }}" {{ request('district_id') == $district->id ? 'selected' : '' }}>
                                            {{ $district->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="recruitment_id">{{ __('Vị trí tuyển dụng') }}</label>
                                <select name="recruitment_id" id="recruitment_id" class="form-control">
                                    <option value="">{{ __('Tất cả vị trí') }}</option>
                                    @foreach($recruitments as $recruitment)
                                        <option value="{{ $recruitment->id }}" {{ request('recruitment_id') == $recruitment->id ? 'selected' : '' }}>
                                            {{ $recruitment->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="status">{{ __('Trạng thái') }}</label>
                                <select name="status" id="status" class="form-control">
                                    <option value="">{{ __('Tất cả trạng thái') }}</option>
                                    @foreach($statuses as $value => $label)
                                        <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> {{ __('Lọc') }}
                                </button>
                                <a href="{{ route('recruitment-applications.index') }}" class="btn btn-secondary">
                                    <i class="fa fa-refresh"></i> {{ __('Đặt lại') }}
                                </a>
                                <a href="{{ route('recruitment-applications.export', request()->all()) }}" class="btn btn-success">
                                    <i class="ti ti-download"></i> {{ __('Xuất Excel') }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            {!! $dataTable->table(['class' => 'table table-striped table-hover vertical-middle']) !!}
        </div>
    </div>
@endsection

@push('footer')
    {!! $dataTable->scripts() !!}

    <script>
        $(document).ready(function() {
            // Handle province change to load districts
            $('#province_id').on('change', function() {
                const provinceId = $(this).val();
                const districtSelect = $('#district_id');

                // Clear district options
                districtSelect.html('<option value="">{{ __("Đang tải...") }}</option>');

                if (provinceId) {
                    // Load districts via AJAX
                    $.ajax({
                        url: '{{ route("ajax.recruitment.get-districts", ":province_id") }}'.replace(':province_id', provinceId),
                        type: 'GET',
                        success: function(response) {
                            if (response.error === false) {
                                const data = response.data;
                                let html = '<option value="">{{ __("Tất cả quận/huyện") }}</option>';
                                $.each(data, function(index, item) {
                                    const selected = '{{ request("district_id") }}' == item.id ? 'selected' : '';
                                    html += '<option value="' + item.id + '" ' + selected + '>' + item.name + '</option>';
                                });
                                districtSelect.html(html);
                            } else {
                                districtSelect.html('<option value="">{{ __("Lỗi tải dữ liệu") }}</option>');
                            }
                        },
                        error: function() {
                            districtSelect.html('<option value="">{{ __("Lỗi tải dữ liệu") }}</option>');
                        }
                    });
                } else {
                    districtSelect.html('<option value="">{{ __("Tất cả quận/huyện") }}</option>');
                }
            });

            // Load districts on page load if province is selected
            @if(request('province_id'))
                $('#province_id').trigger('change');
            @endif

            // Handle export button with current filters
            $(document).on('click', '[data-bb-toggle="export"]', function(e) {
                e.preventDefault();

                // Get current URL with all filters
                const currentUrl = new URL(window.location.href);
                const exportUrl = new URL('{{ route("recruitment-applications.export") }}');

                // Copy all search parameters to export URL
                currentUrl.searchParams.forEach((value, key) => {
                    exportUrl.searchParams.set(key, value);
                });

                // Navigate to export URL
                window.location.href = exportUrl.toString();
            });
        });
    </script>
@endpush

@php
use Botble\Recruitment\Forms\Fronts\RecruitmentApplicationForm;
@endphp

<section class="section-page-recruitment-form section-py">
    <div class="container">
        @if(isset($recruitment))
        {!! RecruitmentApplicationForm::create()->setUrl(route('public.recruitment.application.store'))->setMethod('POST')->renderForm() !!}
        @else
        <div class="text-center">
            <h1 class="title-40 mb-8 font-bold">{{ __('Chọn tin cần ứng tuyển trước.') }}</h1>
        </div>
        @endif
    </div>
</section>

@php
    $recruitment_form_image_thankyou = theme_option('recruitment_form_image_thankyou');
    $recruitment_form_title_thankyou = theme_option('recruitment_form_title_thankyou');
    $recruitment_form_content_thankyou = theme_option('recruitment_form_content_thankyou');
@endphp

<div class="popup-thanks" id="popup-thanks" style="display: none;" data-fancybox-modal>
    <div class="popup-content">
        <div class="popup-main flex flex-col-reverse md:flex-row bg-Primary-Red rounded-tr-3xl rounded-tl-3xl">
            <div class="col-left md:rem:w-[360px] w-full">
                <div class="image"> <a class="img-ratio ratio:pt-[268_360] zoom-img lg:rounded-tl-lg lg:rounded-bl-lg rounded-none" href="#"><img class="lozad undefined" data-src="{{ RvMedia::getImageUrl($recruitment_form_image_thankyou) }}" alt="" /></a></div>
            </div>
            <div class="col-right md:w-[calc(100%-18.75rem)] w-full">
                <div class="title lg:text-32 text-xl font-bold text-Primary-S1 mb-1">{{ $recruitment_form_title_thankyou }}</div>
                <div class="desc lg:text-xl text-[14px] text-justify text-Primary-S1">{!! $recruitment_form_content_thankyou !!}</div>
            </div>
        </div>
    </div>
</div>
{{-- @php
    dd($application);
@endphp --}}

<div class="application-info">
    <div class="row">
        <div class="col-md-6">
            <div class="form-group mb-3">
                <label><strong>Họ và tên:</strong></label>
                <span>{{ $application->full_name }}</span>
            </div>

            <div class="form-group mb-3">
                <label><strong>Email:</strong></label>
                <span><a href="mailto:{{ $application->email }}">{{ $application->email }}</a></span>
            </div>

            <div class="form-group mb-3">
                <label><strong>Số điện thoại:</strong></label>
                <span><a href="tel:{{ $application->phone }}">{{ $application->phone }}</a></span>
            </div>

            <div class="form-group mb-3">
                <label><strong><PERSON><PERSON>y sinh:</strong></label>
                <span>{{ $application->birthday ? $application->birthday->format('d/m/Y') : 'N/A' }}</span>
            </div>

            <div class="form-group mb-3">
                <label><strong>Giới tính:</strong></label>
                <span>{{ $application->gender_label }}</span>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group mb-3">
                <label><strong>Vị trí ứng tuyển:</strong></label>
                <span>{{ $application->recruitment_name ?: ($application->recruitment ? $application->recruitment->name : 'N/A') }}</span>
            </div>

            <div class="form-group mb-3">
                <label><strong>Trạng thái:</strong></label>
                <span>
                    @php
                        switch ($application->status) {
                            case 'pending':
                                $statusClass = 'warning';
                                break;
                            case 'reviewing':
                                $statusClass = 'info';
                                break;
                            case 'approved':
                                $statusClass = 'success';
                                break;
                            case 'rejected':
                                $statusClass = 'danger';
                                break;
                            default:
                                $statusClass = 'secondary';
                                break;
                        }
                    @endphp
                    <span class="badge bg-{{ $statusClass }}">{{ $application->status }}</span>
                </span>
            </div>

            <div class="form-group mb-3">
                <label><strong>Ngày ứng tuyển:</strong></label>
                <span>{{ $application->created_at->format('d/m/Y H:i') }}</span>
            </div>

            @if($application->old_employee_code)
                <div class="form-group mb-3">
                    <label><strong>Mã nhân viên cũ:</strong></label>
                    <span>{{ $application->old_employee_code }}</span>
                </div>
            @endif

            @if($application->referral_code)
                <div class="form-group mb-3">
                    <label><strong>Mã nhân viên giới thiệu:</strong></label>
                    <span>{{ $application->referral_code }}</span>
                </div>
            @endif

            <div class="form-group mb-3">
                <label><strong>Đăng ký nhận bản tin:</strong></label>
                <span class="badge bg-{{ $application->newsletter === 'yes' ? 'success' : 'secondary' }}">
                    {{ $application->newsletter === 'yes' ? 'Có' : 'Không' }}
                </span>
            </div>
        </div>
    </div>

    @if($application->locationProvince1 || $application->locationProvince2 || $application->locationProvince3)
        <hr>
        <h5>Địa điểm làm việc mong muốn</h5>
        <div class="row">
            @if($application->locationProvince1)
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label><strong>Địa điểm 1:</strong></label>
                        <span>
                            {{ $application->locationProvince1->name }}
                            @if($application->locationDistrict1)
                                , {{ $application->locationDistrict1->name }}
                            @endif
                        </span>
                    </div>
                </div>
            @endif

            @if($application->locationProvince2)
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label><strong>Địa điểm 2:</strong></label>
                        <span>
                            {{ $application->locationProvince2->name }}
                            @if($application->locationDistrict2)
                                , {{ $application->locationDistrict2->name }}
                            @endif
                        </span>
                    </div>
                </div>
            @endif

            @if($application->locationProvince3)
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label><strong>Địa điểm 3:</strong></label>
                        <span>
                            {{ $application->locationProvince3->name }}
                            @if($application->locationDistrict3)
                                , {{ $application->locationDistrict3->name }}
                            @endif
                        </span>
                    </div>
                </div>
            @endif
        </div>
    @endif

    <hr>
    <h5>Sở thích làm việc</h5>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group mb-3">
                <label><strong>Có thể làm ca xoay:</strong></label>
                <span class="badge bg-{{ $application->can_work_shifts === 'yes' ? 'success' : 'secondary' }}">
                    {{ $application->can_work_shifts_label }}
                </span>
            </div>

            <div class="form-group mb-3">
                <label><strong>Có thể làm cuối tuần:</strong></label>
                <span class="badge bg-{{ $application->can_work_weekends === 'yes' ? 'success' : 'secondary' }}">
                    {{ $application->can_work_weekends_label }}
                </span>
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group mb-3">
                <label><strong>Có thể làm ngày lễ/tết:</strong></label>
                <span class="badge bg-{{ $application->can_work_holidays === 'yes' ? 'success' : 'secondary' }}">
                    {{ $application->can_work_holidays_label }}
                </span>
            </div>

            <div class="form-group mb-3">
                <label><strong>Số ngày làm việc/tuần:</strong></label>
                <span>{{ $application->work_days_label }}</span>
            </div>
        </div>
    </div>

    <div class="form-group mb-3">
        <label><strong>Thời gian gắn bó mong muốn:</strong></label>
        <span>{{ $application->commitment_duration_label }}</span>
    </div>

    @if($application->cv_file_path && $application->cv_file_name)
        <hr>
        <h5>File CV</h5>
        <div class="form-group mb-3">
            <label><strong>Tên file:</strong></label>
            <span>{{ $application->cv_file_name }}</span>
            @if($application->cv_file_size)
                <small class="text-muted">({{ $application->cv_file_size_formatted }})</small>
            @endif
        </div>
        @if($application->cv_file_path)
            <a href="{{ RvMedia::getImageUrl($application->cv_file_path) }}"
               class="btn btn-sm btn-primary" target="_blank">
                <i class="fa fa-download"></i> Tải xuống CV
            </a>
        @else
            <span class="text-muted">File không tồn tại</span>
        @endif
    @elseif($application->cv_file_name)
        <hr>
        <h5>File CV</h5>
        <div class="form-group mb-3">
            <label><strong>Tên file:</strong></label>
            <span>{{ $application->cv_file_name }}</span>
            <br>
            <span class="text-muted">File không tồn tại trên server</span>
        </div>
    @endif

    @if($application->notes)
        <hr>
        <h5>Ghi chú</h5>
        <div class="form-group mb-3">
            <div>{{ $application->notes }}</div>
        </div>
    @endif

    <hr>
    <div class="action-buttons">
        <a href="{{ route('recruitment-applications.index') }}" class="btn btn-secondary">
            <i class="fa fa-arrow-left"></i> Quay lại danh sách
        </a>

        @if($application->cv_file_path && file_exists(storage_path('app/public/' . $application->cv_file_path)))
            <a href="{{ route('recruitment-applications.download-cv', $application->id) }}"
               class="btn btn-success" target="_blank">
                <i class="fa fa-download"></i> Tải CV
            </a>
        @endif

        @if($application->phone)
            <a href="tel:{{ $application->phone }}" class="btn btn-info">
                <i class="fa fa-phone"></i> Gọi điện
            </a>
        @endif

        @if($application->email)
            <a href="mailto:{{ $application->email }}" class="btn btn-primary">
                <i class="fa fa-envelope"></i> Gửi email
            </a>
        @endif
    </div>
</div>

<style>
.application-info .form-group {
    margin-bottom: 1rem;
}

.application-info label {
    font-weight: 600;
    margin-right: 0.5rem;
    color: #495057;
}

.application-info span {
    color: #212529;
}

.application-info .badge {
    font-size: 0.75em;
}

.application-info hr {
    margin: 1.5rem 0;
    border-color: #dee2e6;
}

.application-info h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.application-info .action-buttons {
    margin-top: 1rem;
}

.application-info .action-buttons .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.application-info .action-buttons .btn:last-child {
    margin-right: 0;
}
</style>

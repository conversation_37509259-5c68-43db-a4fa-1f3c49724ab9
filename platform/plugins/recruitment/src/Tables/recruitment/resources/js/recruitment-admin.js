'use strict';

class RecruitmentAdmin {
    constructor() {
        this.init();
    }

    init() {
        $(document).on('change', '[data-type="province"]', event => {
            event.preventDefault();
            const _self = $(event.currentTarget);
            const provinceId = _self.val();
            
            if (provinceId) {
                $.ajax({
                    url: route('ajax.recruitment.get-districts', { province_id: provinceId }),
                    type: 'GET',
                    success: res => {
                        if (res.error === false) {
                            const data = res.data;
                            let html = '';
                            $.each(data, (index, item) => {
                                html += '<option value="' + item.id + '">' + item.name + '</option>';
                            });
                            $('[data-type="district"]').html(html);
                        } else {
                            Botble.showError(res.message);
                        }
                    },
                    error: error => {
                        Botble.handleError(error);
                    }
                });
            } else {
                $('[data-type="district"]').html('<option value="">' + $('[data-type="district"]').data('placeholder') + '</option>');
            }
        });
    }
}

$(document).ready(() => {
    new RecruitmentAdmin();
});

{"name": "botble/recruitment", "description": "Recruitment plugin for Botble CMS", "type": "package", "require": {"botble/platform": "*@dev"}, "autoload": {"psr-4": {"Botble\\Recruitment\\": "src"}}, "extra": {"laravel": {"providers": ["Botble\\Recruitment\\Providers\\RecruitmentServiceProvider"]}}, "scripts": {"post-install-cmd": ["php artisan cms:plugin:assets:publish recruitment"], "post-update-cmd": ["php artisan cms:plugin:assets:publish recruitment"], "dev": ["npm run dev"], "build": ["npm run build"]}}
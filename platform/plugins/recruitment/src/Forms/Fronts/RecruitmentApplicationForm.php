<?php

namespace Botble\Recruitment\Forms\Fronts;

use Botble\Base\Forms\FieldOptions\EmailFieldOption;
use Botble\Base\Forms\FieldOptions\HtmlFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\EmailField;
use Botble\Base\Forms\Fields\HtmlField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\DateField;
use Botble\Base\Forms\Fields\RadioField;
use Botble\Base\Models\BaseModel;
use Botble\Language\Facades\Language;
use Botble\Recruitment\Http\Requests\RecruitmentApplicationRequest;
use Botble\Recruitment\Models\Province;
use Botble\Recruitment\Models\District;
use Bo<PERSON>ble\Recruitment\Models\Recruitment;
use Botble\Theme\FormFront;
use Botble\Captcha\Facades\Captcha;
use Botble\Captcha\Forms\Fields\ReCaptchaField;


class RecruitmentApplicationForm extends FormFront
{
    protected static ?BaseModel $reference = null;

    public function setup(): void
    {
        $currentLanguage = Language::getCurrentLocaleCode();
        if ($currentLanguage == 'en') {
            $currentLanguage = 'en_US';
        }

        // Lấy recruitment từ parameter URL
        $recruitmentId = request()->input('recruitment_id');
        $recruitment = null;
        $recruitmentName = __('Vui lòng chọn vị trí ứng tuyển'); // Default value

        if ($recruitmentId) {
            $recruitment = Recruitment::query()
                ->join('language_meta', function ($join) use ($currentLanguage) {
                    $join->on('language_meta.reference_id', '=', 'recruitments.id')
                        ->where('language_meta.reference_type', Recruitment::class)
                        ->where('language_meta.lang_meta_code', $currentLanguage);
                })
                ->where('recruitments.id', $recruitmentId)
                ->where('recruitments.status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
                ->select('recruitments.*')
                ->first();

            if ($recruitment) {
                $recruitmentName = $recruitment->name;
                static::$reference = $recruitment;
            }
        }

        $provinces = Province::query()
            ->join('language_meta', function ($join) use ($currentLanguage) {
                $join->on('language_meta.reference_id', '=', 'recruitment_provinces.id')
                    ->where('language_meta.reference_type', Province::class)
                    ->where('language_meta.lang_meta_code', $currentLanguage);
            })
            ->where('recruitment_provinces.status', \Botble\Base\Enums\BaseStatusEnum::PUBLISHED)
            ->orderBy('recruitment_provinces.order')
            ->orderBy('recruitment_provinces.name')
            ->pluck('recruitment_provinces.name', 'recruitment_provinces.id')
            ->all();

        $this
            ->contentOnly()
            ->setFormOption('class', 'wrap-form-recruitment form-style-general')
            ->setUrl(route('public.recruitment.application.store'))
            ->setValidatorClass(RecruitmentApplicationRequest::class)
            ->add('lang_code', 'hidden', ['value' => Language::getCurrentLocale()]);

        // Thêm recruitment_id và recruitment_name từ parameter hoặc reference
        if ($recruitment) {
            $this
                ->add('recruitment_id', 'hidden', ['value' => $recruitment->id])
                ->add('recruitment_name', 'hidden', ['value' => $recruitment->name]);
        } elseif ($recruitmentId) {
            // Nếu có recruitment_id nhưng không tìm thấy recruitment
            $this
                ->add('recruitment_id', 'hidden', ['value' => $recruitmentId])
                ->add('recruitment_name', 'hidden', ['value' => $recruitmentName]);
        }

        // Generate province options
        $provinceOptions = '';
        foreach ($provinces as $id => $name) {
            $provinceOptions .= '<option value="' . $id . '">' . htmlspecialchars($name) . '</option>';
        }

		$vitrituyendung = __('Vị trí tuyển dụng');
		$hoten = __('Họ và tên');
		$ngaysinh = __('Ngày sinh');
		$chonngay = __('Chọn ngày');
		$gioitinh = __('Giới tính');
		$chongioitinh = __('Chọn giới tính');
		$nam = __('Nam');
		$nu = __('Nữ');
		$khac = __('Khác');
		$sodienthoai = __('Số điện thoại');
		$email = __('Email');
		$diadiem = __('Địa điểm');
		$ungcuovi = __('Ứng cử viên được tuyển lại');
		$ma_nhanvien_giothieu = __('Mã nhân viên giới thiệu');
		$diadiem1 = __('Địa điểm làm việc mong muốn 1');
		$diadiem2 = __('Địa điểm làm việc mong muốn 2');
		$diadiem3 = __('Địa điểm làm việc mong muốn 3');

		// Thêm các biến mới
		$nhap_ma_nhanvien_cu = __('Nhập mã nhân viên cũ');
		$nhap_ma_nhanvien_giothieu = __('Nhập mã nhân viên giới thiệu');
		$can_work_shifts = __('Bạn có thể làm ca được không?');
		$can_work_weekends = __('Bạn có thể làm cuối tuần được không?');
		$can_work_holidays = __('Bạn có thể làm trong các ngày Lễ / Tết không?');
		$work_days_per_week = __('Bạn làm được bao nhiêu ngày/tuần ?');
		$commitment_duration = __('Thời gian bạn mong muốn gắn bó với CHAGEE');
		$file_cv = __('File CV');
		$tai_len_cv = __('Tải lên CV của bạn');
		$file_dinh_kem = __('File đính kèm (pdf, doc, docx, xls, xlsx, png, jpg, jpeg)');
		$ung_tuyen_ngay = __('Ứng tuyển ngay');
		$co = __('Có');
		$khong = __('Không');
		$duoi_4_ngay = __('Dưới 4 ngày');
		$tu_4_den_6_ngay = __('4 - 6 ngày');
		$bay_ngay = __('7 ngày');
		$duoi_3_thang = __('Dưới 3 tháng');
		$tu_3_den_6_thang = __('3 - 6 tháng');
		$tren_6_thang = __('Trên 6 tháng');
		$tinh_thanh_pho = __('Tỉnh/Thành phố');
		$quan_huyen = __('Quận/Huyện');

		$dangky_nhan_tin_tuyendung = __('Đăng ký nhận bản tin tuyển dụng');
		$dangky_nhan_tin_tuyendung_yes = __('Có');
		$dangky_nhan_tin_tuyendung_no = __('Không');
		
		$titleForm = __('Form thông tin ứng tuyển');

        $this
            // Title
            ->add('form_application_html', HtmlField::class, HtmlFieldOption::make()
                ->content('
							<div class="title title-40 mb-base text-Primary-S1">' . $titleForm . '</div>
							<div class="wrap-form flex flex-col py-10 lg:px-16 px-5 shadow-Dropshadow-Light rounded-5">
								<table class="basic-information">
									<tr>
										<td>
											<label for="name">' . $vitrituyendung . '</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" name="position_display" value="' . htmlspecialchars($recruitmentName) . '" disabled>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $hoten . ' *</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="' . $hoten . '" name="full_name" required="required"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $ngaysinh . ' *</label>
										</td>
										<td>
											<div class="form-group input-date-picker">
												<input id="calendar" type="text" placeholder="' . $chonngay . '" name="birthday" required>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $gioitinh . ' *</label>
										</td>
										<td>
											<div class="form-group">
												<select name="gender" placeholder="' . $gioitinh . '" required="required">
													<option value="">' . $chongioitinh . '</option>
													<option value="male">' . $nam . '</option>
													<option value="female">' . $nu . '</option>
													<option value="other">' . $khac . '</option>
												</select>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $sodienthoai . ' *</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="' . $sodienthoai . '" name="phone" required="required"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $email . ' *</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="' . $email . '" name="email" required="required"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $diadiem . '</label>
										</td>
										<td>
											<table class="w-full wrap-table-3-columns">
												<tr>
													<td>
														<label for="name">' . $diadiem1 . ' *:</label>
													</td>
													<td>
														<div class="form-group">
															<select name="location_province_1" placeholder="' . $tinh_thanh_pho . '" class="province-select" data-target="location_district_1" required="required">
																<option value=""></option>
																' . $provinceOptions . '
															</select>
														</div>
													</td>
													<td>
														<div class="form-group">
															<select name="location_district_1" placeholder="' . $quan_huyen . '" class="district-select" required="required">
																<option value=""></option>
															</select>
														</div>
													</td>
												</tr>
											</table>
											<table class="w-full wrap-table-3-columns">
												<tr>
													<td>
														<label for="name">' . $diadiem2 . ':</label>
													</td>
													<td>
														<div class="form-group">
															<select name="location_province_2" placeholder="' . $tinh_thanh_pho . '" class="province-select" data-target="location_district_2">
																<option value=""></option>
																' . $provinceOptions . '
															</select>
														</div>
													</td>
													<td>
														<div class="form-group">
															<select name="location_district_2" placeholder="' . $quan_huyen . '" class="district-select">
																<option value=""></option>
															</select>
														</div>
													</td>
												</tr>
											</table>
											<table class="w-full wrap-table-3-columns">
												<tr>
													<td>
														<label for="name">' . $diadiem3 . ':</label>
													</td>
													<td>
														<div class="form-group">
															<select name="location_province_3" placeholder="' . $tinh_thanh_pho . '" class="province-select" data-target="location_district_3">
																<option value=""></option>
																' . $provinceOptions . '
															</select>
														</div>
													</td>
													<td>
														<div class="form-group">
															<select name="location_district_3" placeholder="' . $quan_huyen . '" class="district-select">
																<option value=""></option>
															</select>
														</div>
													</td>
												</tr>
											</table>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $ungcuovi . '</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="' . $nhap_ma_nhanvien_cu . '" name="old_employee_code"/>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label for="name">' . $ma_nhanvien_giothieu . '</label>
										</td>
										<td>
											<div class="form-group">
												<input type="text" placeholder="' . $nhap_ma_nhanvien_giothieu . '" name="referral_code"/>
											</div>
										</td>
									</tr>
								</table>
								<table class="table-checkbox">
									<tr>
										<td>
											<label>' . $dangky_nhan_tin_tuyendung . '</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="newsletter_yes" name="newsletter" value="yes" required>
													<label for="newsletter_yes">' . $dangky_nhan_tin_tuyendung_yes . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="newsletter_no" name="newsletter" value="no">
													<label for="newsletter_no">' . $dangky_nhan_tin_tuyendung_no . '</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>' . $can_work_shifts . '</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="shift_work_yes" name="can_work_shifts" value="yes" required>
													<label for="shift_work_yes">' . $co . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="shift_work_no" name="can_work_shifts" value="no">
													<label for="shift_work_no">' . $khong . '</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>' . $can_work_weekends . '</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="weekend_work_yes" name="can_work_weekends" value="yes" required>
													<label for="weekend_work_yes">' . $co . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="weekend_work_no" name="can_work_weekends" value="no">
													<label for="weekend_work_no">' . $khong . '</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>' . $can_work_holidays . '</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="holiday_work_yes" name="can_work_holidays" value="yes" required>
													<label for="holiday_work_yes">' . $co . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="holiday_work_no" name="can_work_holidays" value="no">
													<label for="holiday_work_no">' . $khong . '</label>
												</div>
											</div>
										</td>
									</tr>
									<tr>
										<td>
											<label>' . $work_days_per_week . '</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="work_days_under_4" name="work_days" value="under_4" required>
													<label for="work_days_under_4">' . $duoi_4_ngay . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="work_days_4_to_6" name="work_days" value="4_to_6">
													<label for="work_days_4_to_6">' . $tu_4_den_6_ngay . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="work_days_7" name="work_days" value="7_days">
													<label for="work_days_7">' . $bay_ngay . '</label>
												</div>
											</div>
										</td>
									</tr>
                                    <tr>
										<td>
											<label>' . $commitment_duration . '</label>
										</td>
										<td>
											<div class="wrap-checkboxs">
												<div class="form-group form-checkbox">
													<input type="radio" id="commitment_under_3" name="commitment_duration" value="under_3_months" required>
													<label for="commitment_under_3">' . $duoi_3_thang . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="commitment_3_to_6" name="commitment_duration" value="3_to_6_months">
													<label for="commitment_3_to_6">' . $tu_3_den_6_thang . '</label>
												</div>
												<div class="form-group form-checkbox">
													<input type="radio" id="commitment_over_6" name="commitment_duration" value="over_6_months">
													<label for="commitment_over_6">' . $tren_6_thang . '</label>
												</div>
											</div>
										</td>
									</tr>
								</table>
								<table class="basic-information mt-5">
									<tr>
										<td>
											<label for="name">' . $file_cv . '*</label>
										</td>
										<td>
											<div class="form-group form-input-file">
												<input type="file" accept=".pdf,.doc,.docx,.png,.jpg,.jpeg, .xlsx, .xls" placeholder="' . $tai_len_cv . '" name="cv_file" required>
											</div>
											<div class="note text-Neutral-800 mt-3">' . $file_dinh_kem . '</div>
										</td>
									</tr>
								</table>
								<div class="frm-submit mt-5 flex items-center justify-between">
									<div class="wrap-captcha"></div>
									<div class="wrap-btn-submit">
										<button class="btn btn-btn-primary secondary w-full flex-center" type="submit"> <span>' . $ung_tuyen_ngay . '</span>
											<div class="btn-icon btn-lined"><span class="icon to-right"><i class="fa-regular fa-arrow-right"></i><i class="fa-regular fa-arrow-right"></i></span></div>
										</button>
									</div>
								</div>
							</div>
						')
                ->toArray());
    }

    public static function createWithReference(BaseModel $model): self
    {
        static::$reference = $model;
        return app(static::class);
    }

    public static function getReference(): ?BaseModel
    {
        return static::$reference;
    }
}

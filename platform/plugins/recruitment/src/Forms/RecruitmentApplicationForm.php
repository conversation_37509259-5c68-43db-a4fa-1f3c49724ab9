<?php

namespace Bo<PERSON>ble\Recruitment\Forms;

use Bo<PERSON>ble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Bo<PERSON>ble\Base\Forms\FieldOptions\StatusFieldOption;
use Bo<PERSON><PERSON>\Recruitment\Models\RecruitmentApplication;
use Bo<PERSON><PERSON>\Recruitment\Enums\RecruitmentApplicationStatusEnum;

class RecruitmentApplicationForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(RecruitmentApplication::class)
            ->addMetaBoxes([
                'information' => [
                    'title' => trans('plugins/recruitment::recruitment.application_information'),
                    'content' => view('plugins/recruitment::application-info', ['application' => $this->getModel()])->render(),
                ],
            ])
            ->add('status', SelectField::class, StatusFieldOption::make()
                ->choices(RecruitmentApplicationStatusEnum::labels())
                ->toArray())
            ->setBreakFieldPoint('status');
    }
}

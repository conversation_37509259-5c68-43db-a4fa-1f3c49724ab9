<?php

namespace Bo<PERSON>ble\Recruitment\Enums;

use Botble\Base\Facades\BaseHelper;
use Botble\Base\Supports\Enum;
use Illuminate\Support\HtmlString;

/**
 * @method static RecruitmentApplicationStatusEnum NEW()
 * @method static RecruitmentApplicationStatusEnum PROCESSING()
 * @method static RecruitmentApplicationStatusEnum PASSED()
 * @method static RecruitmentApplicationStatusEnum FAILED()
 */
class RecruitmentApplicationStatusEnum extends Enum
{
    public const NEW = 'new';

    public const PROCESSING = 'processing';

    public const PASSED = 'passed';

    public const FAILED = 'failed';

    public static $langPath = 'plugins/recruitment::recruitment.application_statuses';

    public function toHtml(): string
    {
        switch ($this->value) {
            case self::NEW:
                $color = 'primary';
                break;
            case self::PROCESSING:
                $color = 'warning';
                break;
            case self::PASSED:
                $color = 'success';
                break;
            case self::FAILED:
                $color = 'danger';
                break;
            default:
                $color = 'secondary';
                break;
        }

        return BaseHelper::renderBadge($this->label(), $color);
    }
}

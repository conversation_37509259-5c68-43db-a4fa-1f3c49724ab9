(()=>{"use strict";var t;(t=jQuery)(document).on("change",'[data-bb-toggle="product-bulk-change"]',(function(){var a=t(this),e=a.closest("table"),c=a.data("id"),n=a.is(":checkbox")||a.is(":radio")?a.is(":checked")?"1":"0":a.val(),o=a.data("column"),i=t('[data-target-id="'.concat(c,'"]'));i.length>0&&(i.hide(),i.each((function(){var a=t(this),e=a.data("target-value").toString();n===e&&a.show()}))),$httpClient.make().withLoading(e[0]).put(a.data("url"),{value:n,column:o}).then((function(t){var a=t.data;Botble.showSuccess(a.message)}))}))})();
(()=>{var t=function(t,a){var i="";$.each(t,(function(t,a){i+=a+"<br />"})),$(a).find(".success-message").html("").hide(),$(a).find(".error-message").html("").hide(),$(a).find(".error-message").html(i).show()};function a(t){this.$container=t,this.$avatarView=this.$container.find(".avatar-view"),this.$avatar=this.$avatarView.find("img"),this.$avatarModal=this.$container.find("#avatar-modal"),this.$loading=this.$container.find(".loading"),this.$avatarForm=this.$avatarModal.find(".avatar-form"),this.$avatarSrc=this.$avatarForm.find(".avatar-src"),this.$avatarData=this.$avatarForm.find(".avatar-data"),this.$avatarInput=this.$avatarForm.find(".avatar-input"),this.$avatarSave=this.$avatarForm.find(".avatar-save"),this.$avatarWrapper=this.$avatarModal.find(".avatar-wrapper"),this.$avatarPreview=this.$avatarModal.find(".avatar-preview"),this.init()}a.prototype={constructor:a,support:{fileList:!!$('<input type="file">').prop("files"),fileReader:!!window.FileReader,formData:!!window.FormData},init:function(){this.support.datauri=this.support.fileList&&this.support.fileReader,this.support.formData||this.initIframe(),this.initModal(),this.addListener()},addListener:function(){this.$avatarView.on("click",$.proxy(this.click,this)),this.$avatarInput.on("change",$.proxy(this.change,this)),this.$avatarForm.on("submit",$.proxy(this.submit,this))},initModal:function(){this.$avatarModal.modal("hide"),this.initPreview()},initPreview:function(){var t=this.$avatar.prop("src");this.$avatarPreview.empty().html('<img src="'+t+'" alt="avatar">')},initIframe:function(){var t="avatar-iframe-"+Math.random().toString().replace(".",""),a=$('<iframe name="'+t+'" style="display:none;"></iframe>'),i=!0,r=this;this.$iframe=a,this.$avatarForm.attr("target",t).after(a),this.$iframe.on("load",(function(){var t,a,e;try{a=this.contentWindow,t=(e=(e=this.contentDocument)||a.document)?e.body.innerText:null}catch(t){}t?r.submitDone(t):i?i=!1:$("#print-msg").text("Image upload failed!").removeClass("hidden"),r.submitEnd()}))},click:function(){this.$avatarModal.modal("show")},change:function(){var t,a;this.support.datauri?(t=this.$avatarInput.prop("files")).length>0&&(a=t[0],this.isImageFile(a)&&this.read(a)):(a=this.$avatarInput.val(),this.isImageFile(a)&&this.syncUpload())},submit:function(){return this.$avatarSrc.val()||this.$avatarInput.val()?this.support.formData?(this.ajaxUpload(),!1):void 0:(alert("Please select image!"),!1)},isImageFile:function(t){return t.type?/^image\/\w+$/.test(t.type):/\.(jpg|jpeg|png|gif)$/.test(t)},read:function(t){var a=this,i=new FileReader;i.readAsDataURL(t),i.onload=function(){a.url=this.result,a.startCropper()}},startCropper:function(){var t=this;this.active?this.$img.cropper("replace",this.url):(this.$img=$('<img src="'+this.url+'" alt="avatar">'),this.$avatarWrapper.empty().html(this.$img),this.$img.cropper({aspectRatio:1,rotatable:!0,preview:this.$avatarPreview.selector,done:function(a){var i=['{"x":'+a.x,'"y":'+a.y,'"height":'+a.height,'"width":'+a.width+"}"].join();t.$avatarData.val(i)}}),this.active=!0)},stopCropper:function(){this.active&&(this.$img.cropper("destroy"),this.$img.remove(),this.active=!1)},ajaxUpload:function(){var a=this.$avatarForm.attr("action"),i=new FormData(this.$avatarForm[0]),r=this;$.ajax(a,{type:"post",data:i,processData:!1,contentType:!1,beforeSend:function(){r.submitStart()},success:function(t){r.submitDone(t)},error:function(a){!function(a,i){if(void 0===a.errors||_.isArray(a.errors))if(void 0!==a.responseJSON)if(void 0!==a.responseJSON.errors)422===a.status&&t(a.responseJSON.errors,i);else if(void 0!==a.responseJSON.message)$(i).find(".error-message").html(a.responseJSON.message).show();else{var r="";$.each(a.responseJSON,(function(t,a){$.each(a,(function(t,a){r+=a+"<br />"}))})),$(i).find(".error-message").html(r).show()}else $(i).find(".error-message").html(a.statusText).show();else t(a.errors,i)}(a)},complete:function(){r.submitEnd()}})},syncUpload:function(){this.$avatarSave.click()},submitStart:function(){this.$loading.fadeIn(),this.$avatarSave.attr("disabled",!0).text("Saving...")},submitDone:function(t){try{t=$.parseJSON(t)}catch(t){}t&&!t.error?t.error?$("#print-msg").text(t.message).removeClass("hidden"):(this.url=t.data.url,this.support.datauri||this.uploaded?(this.uploaded=!1,this.cropDone()):(this.uploaded=!0,this.$avatarSrc.val(this.url),this.startCropper()),this.$avatarInput.val("")):$("#print-msg").text(t.message).removeClass("hidden")},submitEnd:function(){this.$loading.fadeOut(),this.$avatarSave.removeAttr("disabled").text("Save")},cropDone:function(){this.$avatarSrc.val(""),this.$avatarData.val(""),this.$avatar.prop("src",this.url),$(".user-menu img").prop("src",this.url),$(".user.dropdown img").prop("src",this.url),this.stopCropper(),this.initModal()}},$((function(){new a($(".crop-avatar")),$(document).on("change",'[data-bb-toggle="change-customer-avatar"]',(function(t){var a=$(t.currentTarget),i=a.data("url"),r=a[0].files[0];if(void 0!==i&&void 0!==r){var e=new FormData;e.append("avatar_file",r),$.ajax({url:i,type:"POST",data:e,contentType:!1,processData:!1,success:function(t){var a=t.data,i=t.message,r=t.error;"undefined"!=typeof Theme&&r?Theme.showError(i):("undefined"!=typeof Theme&&Theme.showSuccess(i),$('[data-bb-value="customer-avatar"]').prop("src",a.url))}})}}))}))})();
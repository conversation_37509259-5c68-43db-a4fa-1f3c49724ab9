$((function(){$(document).on("click",'[data-bb-toggle="data-export"]',(function(e){e.preventDefault();var t=$(e.currentTarget);$httpClient.make().withButtonLoading(t).withLoading(t.closest(".card")).withResponseType("blob").post(t.attr("href")).then((function(e){var n=e.data,a=document.createElement("a"),o=window.URL.createObjectURL(n);a.href=o,a.download=t.data("filename"),document.body.append(a),a.click(),a.remove(),window.URL.revokeObjectURL(o)}))}))}));
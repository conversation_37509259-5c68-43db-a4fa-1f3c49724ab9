.product-images-wrapper {
    position: relative;
}

.add-new-product-image {
    position: absolute;
    top: -42px;
    right: 20px;
}

.modal {
    .add-new-product-image {
        top: 0;
    }
}

#product-variations-wrapper {
    position: relative;

    .table-hover-variants {
        tr {
            th {
                padding: 8px 12px;
            }
        }
    }

    .wrap-img-product {
        min-height: 40px;
        min-width: 40px;
        max-width: 40px;
        max-height: 40px;
        height: 40px;
        width: 40px;
        text-align: center;
        border: 1px solid rgba(195, 207, 216, 0.3);
        display: table-cell;
        vertical-align: middle;
        background: #fafbfc;
    }

    .wrap-img-product img {
        height: 39px;
        max-width: 100%;
    }
}

.add-new-product-attribute-wrap {
    position: relative;
}

.product-type-digital-management {
    .digital_attachments_input {
        display: none;
    }
    table {
        tbody {
            tr.detach {
                text-decoration: line-through;
                opacity: 0.5;
            }
        }
    }
}

table.dataTable {
    thead {
        tr.dataTable-custom-filter {
            th {
                padding-right: 8px;
                text-transform: unset;
                padding-top: 0;

                .select2-selection__rendered {
                    padding-right: 1.5rem !important;
                    padding-top: 0.35rem !important;
                    padding-bottom: 0.35rem !important;
                }

                .select2-container--default {
                    .select2-selection--single {
                        .select2-selection__arrow {
                            width: 1rem;
                        }
                        .select2-selection__clear {
                            height: 100%;
                            position: absolute;
                            padding: 5px;
                            margin-right: 0;
                            right: 0;
                            z-index: 1;
                            background-color: #73737382;
                            font-size: 0.8rem;
                        }
                    }
                }
            }
        }
    }
}
